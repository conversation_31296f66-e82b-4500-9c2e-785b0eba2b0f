{"playwright.projectDir": "./e2e", "playwright.configFile": "./e2e/playwright.config.ts", "playwright.testDir": "./e2e/tests", "playwright.showTrace": false, "playwright.reuseBrowser": true, "playwright.showBrowser": false, "typescript.preferences.includePackageJsonAutoImports": "on", "files.exclude": {"**/node_modules": true, "**/dist": true, "**/build": true, "**/test-results": true, "**/playwright-report": true}, "search.exclude": {"**/node_modules": true, "**/dist": true, "**/build": true, "**/test-results": true, "**/playwright-report": true}, "typescript.preferences.importModuleSpecifier": "relative"}