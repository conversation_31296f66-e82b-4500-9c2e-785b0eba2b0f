# Products E2E Testing Module

This module contains end-to-end tests for the Products functionality, focusing on the client purchase flow.

## Page Objects

The module uses the Page Object Model (POM) pattern to organize test code. The following page objects are available:

- **ProductsPage**: Represents the products listing page where clients can browse available products
- **ProductDetailsPage**: Represents the product details page where clients can view product information and add to cart
- **CartPage**: Represents the shopping cart page where clients can review and modify their selections
- **CheckoutPage**: Represents the checkout page where clients can complete their purchase

## Test Scenarios

### Client Product Purchase Flow

The main test suite covers the following scenarios:

1. **Client can purchase a product**

   - Navigate to products page
   - Select a product
   - Verify product details
   - Add product to cart
   - Navigate to cart
   - Verify cart contents
   - Proceed to checkout
   - Complete checkout
   - Verify purchase success

2. **Client can update product quantity before purchase**

   - Navigate to products page
   - Select a product
   - Increase quantity
   - Add to cart
   - Navigate to cart
   - Verify cart has correct quantity
   - Update quantity in cart
   - Verify updated quantity

3. **Client can remove product from cart**
   - Navigate to products page
   - Select a product
   - Add to cart
   - Navigate to cart
   - Verify product is in cart
   - Remove product from cart
   - Verify cart is empty

## Running the Tests

To run the tests in this module:

```bash
# Run all tests in the Products module
npx playwright test e2e/modules/Products/tests/

# Run a specific test file
npx playwright test e2e/modules/Products/tests/clientPurchaseProduct.spec.ts

# Run a specific test case
npx playwright test -g "Client can purchase a product"
```

## Test Data

The tests use the following test data:

- Test product: "Premium Health Assessment" with price $199.99
- Test client user: <EMAIL> / password123

## Prerequisites

Before running these tests, ensure:

1. The application is running and accessible
2. Test user accounts are set up in the system
3. Test products are available in the database

## Adding New Tests

When adding new tests to this module:

1. Use the existing page objects or extend them as needed
2. Follow the established patterns for test organization
3. Keep tests independent and focused on specific user flows
4. Add appropriate documentation in this README
