import { Page, Locator, expect } from '@playwright/test';

export class CreateQuestionnairePage {
  readonly page: Page;
  readonly nameInput: Locator;
  readonly titleInput: Locator;
  readonly descriptionInput: Locator;
  readonly saveButton: Locator;
  readonly successAlert: Locator;

  constructor(page: Page) {
    this.page = page;
    this.nameInput = page.locator('[data-testid="questionnaire-name-input"]');
    this.titleInput = page.locator('[data-testid="questionnaire-title-input"]');
    this.descriptionInput = page.locator('[data-testid="questionnaire-description-input"]');
    this.saveButton = page.locator('[data-testid="questionnaire-save-button"]');
    this.successAlert = page.locator('[data-testid="questionnaire-create-success-alert"]');
  }

  async goto() {
    await this.page.goto('/trq/questionnaires/create');
    await expect(this.page).toHaveURL('/trq/questionnaires/create');
    await expect(this.nameInput).toBeVisible(); // Wait for form element
  }

  async fillDetails(name: string, title: string, description: string) {
    await this.nameInput.fill(name);
    await this.titleInput.fill(title);
    await this.descriptionInput.fill(description);
  }

  async save() {
    await this.saveButton.click();
  }

  async verifySuccess() {
    await expect(this.successAlert).toBeVisible({ timeout: 10000 });
    // Also wait for the navigation that happens after the alert
    await expect(this.page).toHaveURL('/trq/questionnaires', { timeout: 15000 });
  }
}
