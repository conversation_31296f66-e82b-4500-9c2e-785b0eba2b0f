import { test, expect } from '@playwright/test';
import { LoginPage } from '../../Authentication/pageObjects/login.page'; // Corrected path
import { QuestionnairePage } from '../pageObjects/questionnaire.page'; // Page object for Questionnaire actions
import { currentEnvironment, testUsers } from '../../../config/e2e.config';
import { Role } from '../../../../src/[types]/Role'; // Import Role enum

test.describe('Questionnaire Module E2E Tests', () => {
  let loginPage: LoginPage;
  let questionnairePage: QuestionnairePage;
  const patientEmail = '<EMAIL>'; // Target patient email

  test.beforeEach(async ({ page, baseURL }) => {
    loginPage = new LoginPage(page);
    questionnairePage = new QuestionnairePage(page);

    // Log in before each test using the Playwright test context's baseURL
    await page.goto(baseURL ?? 'http://localhost:3001');
    await loginPage.loginAs(Role.Admin, baseURL ?? 'http://localhost:3001');
    // Add verification step to ensure login was successful if needed
    // await expect(page).toHaveURL(/.*dashboard/); // Example verification
  });

  test('should create a Questionnaire from a template and assign it to a patient', async ({ page }) => {
    // 1. Navigate to Questionnaire Templates section
    await questionnairePage.navigateToTemplates(); // Implement this method in QuestionnairePage

    // 2. Select a specific template (Need a way to identify a template, e.g., by name or ID)
    const templateName = 'Sample Intake Form'; // Replace with an actual template name
    await questionnairePage.selectTemplate(templateName); // Implement this method

    // 3. Initiate Questionnaire creation from the selected template
    await questionnairePage.createQuestionnaireFromTemplate(); // Implement this method

    // 4. Assign the Questionnaire to the specified patient
    await questionnairePage.assignQuestionnaireToPatient(patientEmail); // Implement this method

    // 5. Verification (Optional but recommended)
    // Navigate to the patient's details or assignments section and verify the questionnaire is listed
    await questionnairePage.verifyQuestionnaireAssigned(patientEmail, templateName); // Implement this method

    // Example assertion: Check for a success message or redirection
    // await expect(page.locator('text=Questionnaire assigned successfully')).toBeVisible();
  });

  // Add more tests for other questionnaire functionalities as needed
});
