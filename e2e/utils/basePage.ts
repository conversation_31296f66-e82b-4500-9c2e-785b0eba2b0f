import { Page, Locator } from '@playwright/test';

/**
 * Base page object that all page objects should extend
 */
export class BasePage {
  readonly page: Page;

  /**
   * Initialize the base page object
   * @param page Playwright page
   */
  constructor(page: Page) {
    this.page = page;
  }

  /**
   * Wait for page to load
   */
  async waitForPageLoad() {
    await this.page.waitForLoadState('networkidle');
  }

  /**
   * Wait for a specific element to be visible
   * @param selector The selector for the element
   */
  async waitForElement(selector: string) {
    await this.page.waitForSelector(selector, { state: 'visible' });
  }

  /**
   * Click an element
   * @param selector The selector for the element
   */
  async click(selector: string) {
    await this.page.click(selector);
  }

  /**
   * Fill a form field
   * @param selector The selector for the form field
   * @param value The value to fill
   */
  async fill(selector: string, value: string) {
    await this.page.fill(selector, value);
  }

  /**
   * Get text content of an element
   * @param selector The selector for the element
   * @returns The text content of the element
   */
  async getText(selector: string): Promise<string> {
    return (await this.page.textContent(selector)) || '';
  }

  /**
   * Check if an element is visible
   * @param selector The selector for the element
   * @returns True if the element is visible, false otherwise
   */
  async isVisible(selector: string): Promise<boolean> {
    return await this.page.isVisible(selector);
  }

  /**
   * Take a screenshot
   * @param name The name of the screenshot
   */
  async takeScreenshot(name: string) {
    await this.page.screenshot({ path: `./test-results/screenshots/${name}.png` });
  }
}
