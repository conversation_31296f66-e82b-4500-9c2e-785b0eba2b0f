#!/usr/bin/env tsx

/**
 * Consolidated Debug Utilities for E2E Tests
 * 
 * This script provides debugging capabilities for:
 * 1. Firebase authentication testing
 * 2. Role-based login testing with UI inspection
 * 3. Environment variable validation
 */

import { chromium } from 'playwright';
import { initializeApp } from 'firebase/app';
import { getAuth, signInWithEmailAndPassword } from 'firebase/auth';
import dotenv from 'dotenv';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import { testUsers } from '../config/e2e.config';

// Get current directory
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Load environment variables from parent directory files
function loadEnvironmentVariables() {
  const envVars: Record<string, string> = {};
  
  // Try loading from project root .env
  const rootEnvPath = path.resolve(__dirname, '../../.env');
  if (fs.existsSync(rootEnvPath)) {
    console.log(`Loading environment variables from ${rootEnvPath}`);
    const result = dotenv.config({ path: rootEnvPath });
    if (!result.error) {
      Object.assign(envVars, result.parsed || {});
    }
  }

  // Try loading from project root .env.development
  const rootDevEnvPath = path.resolve(__dirname, '../../.env.development');
  if (fs.existsSync(rootDevEnvPath)) {
    console.log(`Loading environment variables from ${rootDevEnvPath}`);
    const result = dotenv.config({ path: rootDevEnvPath });
    if (!result.error) {
      Object.entries(result.parsed || {}).forEach(([key, value]) => {
        if (!envVars[key]) {
          envVars[key] = value;
        }
      });
    }
  }

  return envVars;
}

// Debug Firebase authentication
export async function debugFirebaseAuth() {
  console.log('\n===== FIREBASE AUTHENTICATION DEBUG =====');
  
  try {
    const firebaseConfig = {
      apiKey: process.env.VITE_APP_FIREBASE_API_KEY,
      authDomain: process.env.VITE_APP_FIREBASE_AUTH_DOMAIN,
      projectId: process.env.VITE_APP_FIREBASE_PROJECT_ID,
      storageBucket: process.env.VITE_APP_FIREBASE_STORAGE_BUCKET,
      messagingSenderId: process.env.VITE_APP_FIREBASE_MESSAGING_SENDER_ID,
      appId: process.env.VITE_APP_FIREBASE_APP_ID,
    };

    const app = initializeApp(firebaseConfig);
    const auth = getAuth(app);
    
    console.log('✅ Firebase initialized successfully');
    
    // Test login with admin user
    const adminCredentials = testUsers.admin;
    const userCredential = await signInWithEmailAndPassword(auth, adminCredentials.email, adminCredentials.password);
    console.log(`✅ Login successful for ${userCredential.user.email}`);
    
    return true;
  } catch (error) {
    console.error('❌ Firebase authentication error:', error);
    return false;
  }
}

// Debug role-based login with UI inspection
export async function debugRoleLogin(role: keyof typeof testUsers) {
  console.log(`\n===== DEBUGGING LOGIN FOR ROLE: ${role.toUpperCase()} =====`);
  
  const credentials = testUsers[role];
  const baseURL = process.env.BASE_URL || 'http://localhost:3001';
  
  console.log(`Using email: ${credentials.email}`);
  
  const browser = await chromium.launch({
    headless: false,
    slowMo: 500
  });

  const context = await browser.newContext({
    viewport: { width: 1920, height: 1080 },
    recordVideo: { dir: '../test-results/debug-videos/' }
  });

  const page = await context.newPage();

  try {
    // Navigate to login page
    console.log('Navigating to login page...');
    await page.goto(`${baseURL}/trq/login`);

    // Fill login form
    console.log('Filling login form...');
    await page.getByTestId('login-email-input').fill(credentials.email);
    await page.getByTestId('login-password-input').fill(credentials.password);
    
    // Take screenshot before login
    await page.screenshot({ path: `../test-results/debug-${role}-before-login.png` });
    
    // Submit login
    console.log('Submitting login...');
    await page.getByTestId('login-submit-button').click();
    
    // Wait for navigation
    await page.waitForURL(`**${baseURL}/trq/**`);
    
    // Take screenshot after login
    await page.screenshot({ path: `../test-results/debug-${role}-after-login.png` });
    
    console.log(`Current URL: ${page.url()}`);
    
    // List all data-testid elements
    const dataTestIds = await page.evaluate(() => {
      const elements = document.querySelectorAll('[data-testid]');
      return Array.from(elements).map(el => el.getAttribute('data-testid'));
    });
    
    console.log('\nFound data-testid elements:');
    dataTestIds.forEach(id => console.log(`- ${id}`));
    
    console.log('\nPress any key to close the browser...');
    await new Promise(resolve => process.stdin.once('data', resolve));
    
  } catch (error) {
    console.error('Error during debug:', error);
    await page.screenshot({ path: `../test-results/debug-${role}-error.png` });
  } finally {
    await browser.close();
  }
}

// Main CLI interface
async function main() {
  const args = process.argv.slice(2);
  const command = args[0];
  
  // Load environment variables
  loadEnvironmentVariables();
  
  switch (command) {
    case 'auth':
      await debugFirebaseAuth();
      break;
    case 'login':
      const role = args[1] as keyof typeof testUsers;
      if (!role || !testUsers[role]) {
        console.error('Please specify a valid role: admin, clinicAdmin, doctor, client, patient');
        process.exit(1);
      }
      await debugRoleLogin(role);
      break;
    default:
      console.log('Usage:');
      console.log('  tsx debug-utils.ts auth                    # Debug Firebase authentication');
      console.log('  tsx debug-utils.ts login <role>           # Debug role-based login');
      console.log('');
      console.log('Available roles: admin, clinicAdmin, doctor, client, patient');
  }
}

// Run if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  main().catch(console.error);
}
