import { test, expect } from '@playwright/test';
import { ProductsPage, ProductDetailsPage, CartPage, CheckoutPage } from '../../pages/Products/index';

/**
 * Test suite for client purchasing a product
 */
test.describe('Client Product Purchase Flow', () => {
  let productsPage: ProductsPage;
  let productDetailsPage: ProductDetailsPage;
  let cartPage: CartPage;
  let checkoutPage: CheckoutPage;

  // Test data
  const testProduct = {
    name: 'Premium Health Assessment',
    price: '$199.99'
  };

  test.beforeEach(async ({ page }) => {
    // Initialize page objects
    productsPage = new ProductsPage(page);
    productDetailsPage = new ProductDetailsPage(page);
    cartPage = new CartPage(page);
    checkoutPage = new CheckoutPage(page);

    // Login as a client user (assuming auth state is already set up)
    // This would typically use a helper function or fixture to set the auth state
    await page.goto('/trq/login');
    await page.fill('[data-testid="email-input"]', '<EMAIL>');
    await page.fill('[data-testid="password-input"]', 'password123');
    await page.click('[data-testid="login-button"]');
    await page.waitForURL('**/trq/dashboard');
  });

  test('Client can purchase a product', async () => {
    // Step 1: Navigate to the products page
    await productsPage.goto();
    await productsPage.verifyProductsDisplayed();

    // Step 2: Select a product
    await productsPage.clickProductByTitle(testProduct.name);
    await productDetailsPage.waitForPageLoad();

    // Step 3: Verify product details
    await productDetailsPage.verifyProductDetails(testProduct.name, testProduct.price);

    // Step 4: Add product to cart
    await productDetailsPage.addToCart();

    // Step 5: Navigate to cart
    await cartPage.goto();
    await cartPage.waitForPageLoad();

    // Step 6: Verify cart contents
    await cartPage.verifyProductInCart(testProduct.name, 1);

    // Step 7: Proceed to checkout
    await cartPage.proceedToCheckout();
    await checkoutPage.waitForPageLoad();

    // Step 8: Complete checkout
    const orderPlaced = await checkoutPage.completeCheckout();

    // Step 9: Verify purchase success
    expect(orderPlaced).toBeTruthy();
    const orderNumber = await checkoutPage.getOrderNumber();
    expect(orderNumber).toBeTruthy();

    // Optional: Take a screenshot of the order confirmation
    await checkoutPage.takeScreenshot('order-confirmation');
  });

  test('Client can update product quantity before purchase', async () => {
    // Step 1: Navigate to the products page
    await productsPage.goto();

    // Step 2: Select a product
    await productsPage.clickProductByTitle(testProduct.name);
    await productDetailsPage.waitForPageLoad();

    // Step 3: Increase quantity
    await productDetailsPage.increaseQuantity(2); // Increase to 3 total

    // Step 4: Add to cart
    await productDetailsPage.addToCart();

    // Step 5: Navigate to cart
    await cartPage.goto();

    // Step 6: Verify cart has correct quantity
    await cartPage.verifyProductInCart(testProduct.name, 3);

    // Step 7: Update quantity in cart
    const firstItemIndex = 0;
    await cartPage.updateItemQuantity(firstItemIndex, 2);

    // Step 8: Verify updated quantity
    await cartPage.verifyProductInCart(testProduct.name, 2);
  });

  test('Client can remove product from cart', async () => {
    // Step 1: Navigate to the products page
    await productsPage.goto();

    // Step 2: Select a product
    await productsPage.clickProductByTitle(testProduct.name);

    // Step 3: Add to cart
    await productDetailsPage.addToCart();

    // Step 4: Navigate to cart
    await cartPage.goto();

    // Step 5: Verify product is in cart
    await cartPage.verifyProductInCart(testProduct.name, 1);

    // Step 6: Remove product from cart
    const firstItemIndex = 0;
    await cartPage.removeItem(firstItemIndex);

    // Step 7: Verify cart is empty
    const isEmpty = await cartPage.isCartEmpty();
    expect(isEmpty).toBeTruthy();
  });
});
