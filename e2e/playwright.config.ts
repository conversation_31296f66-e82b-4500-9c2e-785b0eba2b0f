import { PlaywrightTestConfig } from '@playwright/test';
import { currentEnvironment, timeouts } from './config/e2e.config';

/**
 * Playwright configuration for TRQ (The Respiratory Questionnaire) testing
 */
const config: PlaywrightTestConfig = {
  // Specify the directory for tests
  testDir: './[tests]',

  // Global timeout for each test (in milliseconds)
  timeout: timeouts.navigation,

  expect: {
    // Timeout for expect assertions
    timeout: timeouts.expectation
  },

  // Run tests in parallel if possible
  fullyParallel: false,

  // Forbid 'test.only' in CI environments to ensure all tests run
  forbidOnly: Boolean(process.env.CI),

  // Retry failed tests in CI (2 retries) and no retries locally
  retries: process.env.CI ? 2 : 0,

  // Limit workers to 1 in CI; let <PERSON>wright decide locally
  workers: 1,

  // Configure reporters
  reporter: [
    ['html', { open: 'never' }], // Generate an HTML report but do not auto-open it
    ['list'] // Display each test and its status
  ],

  use: {
    // Use baseURL from configuration
    baseURL: currentEnvironment.baseUrl,

    // Enable trace collection on the first retry of a test
    trace: 'on-first-retry',

    // Record a video on the first retry for debugging purposes
    video: 'on-first-retry',

    // Take a screenshot only when a test fails
    screenshot: 'only-on-failure',

    // Use a custom attribute for test IDs
    testIdAttribute: 'data-testid',

    // Define a consistent viewport size
    viewport: { width: 2560, height: 1440 },

    // Action and navigation timeouts from config
    actionTimeout: timeouts.action,
    navigationTimeout: timeouts.navigation
  },

  // Directory where Playwright will save test artifacts (videos, screenshots, etc.)
  outputDir: './test-results'
};

export default config;
