import { chromium } from '@playwright/test';
import accounts from '../.auth/accounts.json' assert { type: 'json' };
import { currentEnvironment } from '../config/e2e.config';

const BASE_URL = currentEnvironment.baseUrl;

/**
 * This script creates authentication state files for different user types
 * Run this before tests to create the necessary authentication files
 */
async function createAuthFile(userType: 'admin' | 'patient' | 'provider') {
  if (!accounts[userType]) {
    console.error(`No account credentials found for ${userType}`);
    process.exit(1);
  }

  console.log(`Setting up auth for ${userType} user...`);

  // Launch a browser
  const browser = await chromium.launch({ headless: true });
  const context = await browser.newContext();
  const page = await context.newPage();

  try {
    // Navigate to the login page
    await page.goto(`${BASE_URL}/trq/login`); // Corrected path

    // Fill login form
    await page.waitForSelector('[data-testid="login-email-input"]');
    await page.fill('[data-testid="login-email-input"]', accounts[userType].email);
    await page.fill('[data-testid="login-password-input"]', accounts[userType].password);

    // Submit form
    await page.click('[data-testid="login-submit-button"]');

    // Wait for successful login
    await page.waitForURL(`${BASE_URL}/trq/**`, { timeout: 30000 });

    // Save the authentication state
    await context.storageState({ path: `e2e/.auth/${userType}.json` });
    console.log(`Successfully created auth file for ${userType}`);
  } catch (error) {
    console.error(`Error creating auth file for ${userType}:`, error);
    process.exit(1);
  } finally {
    await browser.close();
  }
}

// Main function to setup all auth files
async function setupAuth() {
  // Create auth files for all user types
  await createAuthFile('admin');
  await createAuthFile('patient');
  await createAuthFile('provider');

  console.log('Auth setup complete!');
}

// Run the setup
setupAuth().catch(error => {
  console.error('Auth setup failed:', error);
  process.exit(1);
});
