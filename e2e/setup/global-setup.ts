import { FullConfig } from '@playwright/test';
import { createUserWithEmailAndPassword } from 'firebase/auth';
import * as dotenv from 'dotenv';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

// Get current directory
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const e2eDir = path.resolve(__dirname, '..');

// Try loading from e2e directory first
const e2eEnvPath = path.resolve(e2eDir, '.env');
if (fs.existsSync(e2eEnvPath)) {
  console.log(`Loading environment variables from ${e2eEnvPath}`);
  dotenv.config({ path: e2eEnvPath });
} else {
  console.log('No .env file found in e2e directory');
}

import { testUsers } from '../config/e2e.config';
import { auth } from '../config/trq-e2e-firebase-config';

async function globalSetup(config: FullConfig) {
  console.log('Starting global setup...');

  // Log environment variables for debugging (mask sensitive values)
  console.log('Environment variables for E2E tests:');
  [
    'VITE_APP_FIREBASE_API_KEY',
    'VITE_APP_FIREBASE_AUTH_DOMAIN',
    'VITE_APP_FIREBASE_PROJECT_ID'
  ].forEach(key => {
    const value = process.env[key];
    if (!value) {
      console.log(`  ${key}: MISSING`);
    } else {
      const maskedValue = key.includes('KEY') || key.includes('ID')
        ? `${value.substring(0, 3)}...${value.substring(value.length - 3)}`
        : value;
      console.log(`  ${key}: ${maskedValue}`);
    }
  });

  // --- Ensure Admin User Exists ---
  // Use environment variables for credentials if available, otherwise fall back to defaults
  const adminEmail = process.env.E2E_ADMIN_EMAIL || testUsers.admin.email;
  const adminPassword = process.env.E2E_ADMIN_PASSWORD || testUsers.admin.password;
  console.log(`Attempting to ensure admin user exists: ${adminEmail}`);

  try {
    await createUserWithEmailAndPassword(auth, adminEmail, adminPassword);
    console.log(`Admin user ${adminEmail} created successfully.`);
  } catch (error: any) {
    if (error.code === 'auth/email-already-in-use') {
      console.log(`Admin user ${adminEmail} already exists.`);
    } else {
      // Throw any other unexpected errors
      console.error(`Error ensuring admin user exists: ${error.code} - ${error.message}`);
      throw error;
    }
  }
  console.log('Admin user check complete.');
}

export default globalSetup;
