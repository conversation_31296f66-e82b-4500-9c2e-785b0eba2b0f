import { E2EConfig } from './types';

/**
 * Consolidated E2E Test Configuration
 * This file replaces the previous config.ts and environment.json files
 */

// Helper function to get environment variable with fallback
function getEnvVar(key: string, fallback: string): string {
  return process.env[key] || fallback;
}

// Helper function to get test user credentials from environment
function getTestUserCredentials() {
  return {
    admin: {
      email: getEnvVar('E2E_ADMIN_EMAIL', '<EMAIL>'),
      password: getEnvVar('E2E_ADMIN_PASSWORD', 'password123')
    },
    clinicAdmin: {
      email: getEnvVar('E2E_CLINIC_ADMIN_EMAIL', '<EMAIL>'),
      password: getEnvVar('E2E_CLINIC_ADMIN_PASSWORD', 'password123')
    },
    doctor: {
      email: getEnvVar('E2E_DOCTOR_EMAIL', '<EMAIL>'),
      password: getEnvVar('E2E_DOCTOR_PASSWORD', 'password123')
    },
    client: {
      email: getEnvVar('E2E_CLIENT_EMAIL', '<EMAIL>'),
      password: getEnvVar('E2E_CLIENT_PASSWORD', 'password123')
    },
    patient: {
      email: getEnvVar('E2E_PATIENT_EMAIL', '<EMAIL>'),
      password: getEnvVar('E2E_PATIENT_PASSWORD', 'password123')
    }
  };
}

export const e2eConfig: E2EConfig = {
  environments: {
    development: {
      baseUrl: getEnvVar('BASE_URL', 'http://localhost:3001'),
      apiUrl: getEnvVar('API_URL', 'http://localhost:3001/api')
    },
    staging: {
      baseUrl: 'https://staging.trq.com',
      apiUrl: 'https://staging.trq.com/api'
    },
    production: {
      baseUrl: 'https://trq.com',
      apiUrl: 'https://trq.com/api'
    }
  },
  defaultEnvironment: (getEnvVar('NODE_ENV', 'development') as 'development' | 'staging' | 'production'),
  timeouts: {
    navigation: 30000,
    action: 15000,
    expectation: 10000,
    default: 10000
  },
  testUsers: getTestUserCredentials(),
  retries: 2,
  waitForAnimations: true,
  waitForNavigation: true
};

// Export current environment config
export const currentEnvironment = e2eConfig.environments[e2eConfig.defaultEnvironment];

// Export test users for easy access
export const testUsers = e2eConfig.testUsers;

// Export timeouts for easy access
export const timeouts = e2eConfig.timeouts;

export default e2eConfig;
