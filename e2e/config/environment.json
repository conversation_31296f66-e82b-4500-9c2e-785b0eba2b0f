{"type": "json", "environments": {"development": {"baseUrl": "http://localhost:3000", "apiUrl": "http://localhost:3000/api"}, "staging": {"baseUrl": "https://staging.trq.com", "apiUrl": "https://staging.trq.com/api"}, "production": {"baseUrl": "https://trq.com", "apiUrl": "https://trq.com/api"}}, "defaultEnvironment": "development", "timeouts": {"navigation": 30000, "action": 10000, "expectation": 5000, "defaultTimeout": 10000, "navigationTimeout": 10000, "actionTimeout": 10000}, "baseUrl": "/trq", "credentials": {"admin": {"email": "<EMAIL>", "password": "password123"}}}