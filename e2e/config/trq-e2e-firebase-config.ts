// Import the functions you need from the SDKs you need
import { initializeApp } from 'firebase/app';
import { getAuth } from 'firebase/auth';
import { getFirestore } from 'firebase/firestore';
import { getStorage } from 'firebase/storage';
import dotenv from 'dotenv';
import path from 'path';
import { fileURLToPath } from 'url';
import fs from 'fs';

// Get current directory and load environment variables
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const e2eDir = path.resolve(__dirname, '..');

// Try loading from e2e directory first
const e2eEnvPath = path.resolve(e2eDir, '.env');
if (fs.existsSync(e2eEnvPath)) {
  console.log(`Loading environment variables from ${e2eEnvPath}`);
  dotenv.config({ path: e2eEnvPath });
} else {
  // Fallback to project root if no .env file in e2e directory
  const rootEnvPath = path.resolve(e2eDir, '../.env');
  if (fs.existsSync(rootEnvPath)) {
    console.log(`Loading environment variables from ${rootEnvPath}`);
    dotenv.config({ path: rootEnvPath });
  }

  const rootDevEnvPath = path.resolve(e2eDir, '../.env.development');
  if (fs.existsSync(rootDevEnvPath)) {
    console.log(`Loading environment variables from ${rootDevEnvPath}`);
    dotenv.config({ path: rootDevEnvPath });
  }
}

// Your web app's Firebase configuration
const firebaseConfig = {
  apiKey: process.env.VITE_APP_FIREBASE_API_KEY,
  authDomain: process.env.VITE_APP_FIREBASE_AUTH_DOMAIN,
  projectId: process.env.VITE_APP_FIREBASE_PROJECT_ID,
  storageBucket: process.env.VITE_APP_FIREBASE_STORAGE_BUCKET,
  messagingSenderId: process.env.VITE_APP_FIREBASE_MESSAGING_SENDER_ID,
  appId: process.env.VITE_APP_FIREBASE_APP_ID,
  measurementId: process.env.VITE_APP_FIREBASE_MEASUREMENT_ID
};

// Log configuration for debugging (mask sensitive values)
console.log('Firebase config for E2E tests:', {
  apiKey: firebaseConfig.apiKey ? `${firebaseConfig.apiKey.substring(0, 3)}...${firebaseConfig.apiKey.substring(firebaseConfig.apiKey.length - 3)}` : 'MISSING',
  authDomain: firebaseConfig.authDomain || 'MISSING',
  projectId: firebaseConfig.projectId || 'MISSING',
  storageBucket: firebaseConfig.storageBucket || 'MISSING',
  messagingSenderId: firebaseConfig.messagingSenderId ? `${firebaseConfig.messagingSenderId.substring(0, 3)}...` : 'MISSING',
  appId: firebaseConfig.appId ? `${firebaseConfig.appId.substring(0, 3)}...` : 'MISSING'
});

// Initialize Firebase
const app = initializeApp(firebaseConfig);

// Initialize Firebase Authentication and get a reference to the service
export const auth = getAuth(app);
export const db = getFirestore(app, process.env.VITE_APP_DATABASE_NAME || '(default)');
export const storage = getStorage(app);

export default app;
