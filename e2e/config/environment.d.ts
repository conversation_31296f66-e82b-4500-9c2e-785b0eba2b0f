declare module './environment.json' {
  interface Environment {
    baseUrl: string;
    apiUrl: string;
  }

  interface Timeouts {
    navigation: number;
    action: number;
    expectation: number;
  }

  const value: {
    environments: {
      development: Environment;
      staging: Environment;
      production: Environment;
    };
    defaultEnvironment: 'development' | 'staging' | 'production';
    timeouts: Timeouts;
  };

  export default value;
} 