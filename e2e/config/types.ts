export interface EnvironmentConfig {
  baseUrl: string;
  apiUrl: string;
}

export interface Timeouts {
  navigation: number;
  action: number;
  expectation: number;
  defaultTimeout: number;
  navigationTimeout: number;
  actionTimeout: number;
}

export interface Credentials {
  email: string;
  password: string;
}

export interface AuthConfig {
  admin: Credentials;
}

export interface Environment {
  environments: {
    development: EnvironmentConfig;
    staging: EnvironmentConfig;
    production: EnvironmentConfig;
  };
  defaultEnvironment: string;
  timeouts: Timeouts;
  baseUrl: string;
  credentials: AuthConfig;
} 