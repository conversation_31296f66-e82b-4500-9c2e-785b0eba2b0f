export interface EnvironmentConfig {
  baseUrl: string;
  apiUrl: string;
}

export interface Timeouts {
  navigation: number;
  action: number;
  expectation: number;
  default: number;
}

export interface Credentials {
  email: string;
  password: string;
}

export interface TestUsers {
  admin: Credentials;
  clinicAdmin: Credentials;
  doctor: Credentials;
  client: Credentials;
  patient: Credentials;
}

export interface E2EConfig {
  environments: {
    development: EnvironmentConfig;
    staging: EnvironmentConfig;
    production: EnvironmentConfig;
  };
  defaultEnvironment: 'development' | 'staging' | 'production';
  timeouts: Timeouts;
  testUsers: TestUsers;
  retries: number;
  waitForAnimations: boolean;
  waitForNavigation: boolean;
}