# TRQ E2E Tests

This directory contains end-to-end tests for the TRQ (The Respiratory Questionnaire) application.

## Directory Structure

```
e2e/
├── [tests]/                    # Test modules organized by feature
│   ├── Authentication/         # Authentication tests
│   ├── Layout/                 # Layout and UI tests
│   ├── Navigation/             # Navigation tests
│   ├── Products/               # Product-related tests
│   └── Questionnaire/          # Questionnaire tests
├── config/                     # Configuration files
│   ├── e2e.config.ts          # Main E2E configuration
│   ├── types.ts               # TypeScript type definitions
│   └── trq-e2e-firebase-config.ts # Firebase configuration
├── debug/                      # Debug utilities
│   └── debug-utils.ts         # Consolidated debug tools
├── scripts/                    # Database and user setup scripts
├── setup/                      # Playwright setup files
├── types/                      # Additional type definitions
├── utils/                      # Utility functions
├── playwright.config.ts        # Playwright configuration
└── package.json               # Dependencies and scripts
```

## Available Scripts

### Test Execution
- `npm run test` - Run all tests
- `npm run test:auth` - Run authentication tests only
- `npm run test:navigation` - Run navigation tests only
- `npm run test:products` - Run product tests only
- `npm run test:questionnaire` - Run questionnaire tests only
- `npm run test:layout` - Run layout tests only

### Debug Tools
- `npm run debug:auth` - Debug Firebase authentication
- `npm run debug:login <role>` - Debug role-based login (admin, clinicAdmin, doctor, client, patient)

### Database and User Management
- `npm run setup:database` - Populate test database
- `npm run setup:users` - Create test users
- `npm run setup:auth-states` - Create authentication states
- `npm run setup:all` - Run all setup scripts
- `npm run reset:database` - Reset development database
- `npm run wipe:auth` - Remove all test users

### Reports
- `npm run report` - Show Playwright test report

## Configuration

The E2E tests use a consolidated configuration system located in `config/e2e.config.ts`. This file:

- Manages environment-specific settings (development, staging, production)
- Defines test user credentials
- Sets timeout values
- Configures base URLs and API endpoints

### Environment Variables

Create a `.env` file in the project root with the following variables:

```env
# Firebase Configuration
VITE_APP_FIREBASE_API_KEY=your_api_key
VITE_APP_FIREBASE_AUTH_DOMAIN=your_auth_domain
VITE_APP_FIREBASE_PROJECT_ID=your_project_id
VITE_APP_FIREBASE_STORAGE_BUCKET=your_storage_bucket
VITE_APP_FIREBASE_MESSAGING_SENDER_ID=your_sender_id
VITE_APP_FIREBASE_APP_ID=your_app_id

# Test User Credentials
E2E_ADMIN_EMAIL=<EMAIL>
E2E_ADMIN_PASSWORD=password123
E2E_CLINIC_ADMIN_EMAIL=<EMAIL>
E2E_CLINIC_ADMIN_PASSWORD=password123
E2E_DOCTOR_EMAIL=<EMAIL>
E2E_DOCTOR_PASSWORD=password123
E2E_CLIENT_EMAIL=<EMAIL>
E2E_CLIENT_PASSWORD=password123
E2E_PATIENT_EMAIL=<EMAIL>
E2E_PATIENT_PASSWORD=password123

# Base URL
BASE_URL=http://localhost:3001
```

## Test Organization

Tests are organized by feature modules in the `[tests]` directory. Each module contains:

- `tests/` - Test specification files
- `pageObjects/` - Page object models (if applicable)

## Debug Tools

The `debug/debug-utils.ts` script provides utilities for:

1. **Firebase Authentication Testing**: Verify Firebase connection and test user authentication
2. **Role-based Login Testing**: Test login functionality for different user roles with UI inspection

### Usage Examples

```bash
# Debug Firebase authentication
npm run debug:auth

# Debug admin login with UI inspection
npm run debug:login admin

# Debug client login with UI inspection
npm run debug:login client
```

## Scripts Directory

The `scripts/` directory contains utilities for:

- Creating and managing test users
- Populating test databases
- Resetting development environments
- Managing authentication states

All scripts are written in TypeScript and can be executed using the npm scripts defined in `package.json`.

## Best Practices

1. **Use data-testid attributes** for element selection in tests
2. **Organize tests by feature** in the appropriate module directory
3. **Use the consolidated configuration** instead of hardcoded values
4. **Run setup scripts** before executing tests to ensure proper test data
5. **Use debug tools** when troubleshooting test failures

## Dependencies

- **@playwright/test**: Test framework
- **playwright**: Browser automation
- **firebase**: Firebase SDK for authentication testing
- **dotenv**: Environment variable management
- **tsx**: TypeScript execution
- **typescript**: TypeScript compiler
