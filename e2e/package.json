{"name": "trq-e2e-tests", "version": "1.0.0", "description": "End-to-end tests for TRQ application", "type": "module", "scripts": {"test": "playwright test", "test:auth": "playwright test [tests]/Authentication/tests/", "test:navigation": "playwright test [tests]/Navigation/", "test:products": "playwright test [tests]/Products/tests/", "test:questionnaire": "playwright test [tests]/Questionnaire/tests/", "test:layout": "playwright test [tests]/Layout/", "debug:auth": "tsx debug/debug-utils.ts auth", "debug:login": "tsx debug/debug-utils.ts login", "report": "playwright show-report", "setup:database": "tsx scripts/populate-database.ts", "setup:users": "tsx scripts/create-test-users.ts", "setup:auth-states": "tsx scripts/create-auth-states.ts", "setup:all": "tsx scripts/run-all-e2e-setup.ts", "reset:database": "tsx scripts/reset-dev-database.ts", "wipe:auth": "tsx scripts/wipe-auth-users.ts"}, "dependencies": {"dotenv": "^16.0.3", "firebase": "^10.0.0", "playwright": "^1.35.0"}, "devDependencies": {"@playwright/test": "^1.35.0", "tsx": "^3.12.7", "typescript": "^5.0.4"}}