import { chromium } from '@playwright/test';
import * as fs from 'fs';
import * as path from 'path';
import { Role } from '../../src/[types]/Role';
import { createAuthStorageState, getStorageStatePath } from '../utils/auth-utils';
import { currentEnvironment } from '../config/e2e.config';

/**
 * This script creates authentication state files for each role
 * These state files can be used to skip the login process in tests
 * 
 * Improved version with better error handling and retry logic
 */
async function createAuthStates() {
  console.log('Creating authentication state files for all roles...');

  // Ensure the auth directory exists
  const authDir = path.join(process.cwd(), '.auth');
  if (!fs.existsSync(authDir)) {
    console.log(`Creating auth directory: ${authDir}`);
    fs.mkdirSync(authDir, { recursive: true });
  }

  // Create a browser with more relaxed timeout
  const browser = await chromium.launch({
    timeout: 60000,
    args: ['--no-sandbox', '--disable-setuid-sandbox', '--disable-dev-shm-usage']
  });

  // Track results
  const results = {
    success: 0,
    failed: 0,
    roles: [] as string[]
  };

  // Handle each role separately with retries
  const maxRetries = 2;

  // Process admin role first as it's most critical
  await processRole(Role.Admin, browser, maxRetries, results);

  // Process other roles
  const otherRoles = Object.values(Role).filter(r => r !== Role.Admin);
  for (const role of otherRoles) {
    await processRole(role, browser, maxRetries, results);
  }

  // Close the browser
  await browser.close();

  // Report results
  console.log('\n========== Authentication State Creation Summary ==========');
  console.log(`Successful: ${results.success}`);
  console.log(`Failed: ${results.failed}`);
  console.log(`Created auth states for: ${results.roles.join(', ')}`);
  console.log('==========================================================\n');

  return results;
}

/**
 * Process a single role with retries
 */
async function processRole(role: Role, browser: any, maxRetries: number, results: any) {
  console.log(`\n📝 Processing authentication state for role: ${role}`);

  for (let attempt = 0; attempt <= maxRetries; attempt++) {
    if (attempt > 0) {
      console.log(`🔄 Retry attempt ${attempt}/${maxRetries} for ${role}...`);
    }

    try {
      // Create a new context for this role
      const context = await browser.newContext();
      const page = await context.newPage();

      // Configure longer timeouts
      page.setDefaultTimeout(30000);
      page.setDefaultNavigationTimeout(30000);

      // Create the auth state file path
      const statePath = getStorageStatePath(role);
      const fullPath = path.join(process.cwd(), statePath);

      // Create the auth state by logging in
      await createAuthStorageState(page, role as Role, fullPath, config.baseUrl); // Add config.baseUrl

      // Verify the file was created
      if (fs.existsSync(fullPath)) {
        console.log(`✅ Successfully created auth state for ${role} at ${statePath}`);
        results.success++;
        results.roles.push(role.toString());

        // Close the page and context
        await page.close();
        await context.close();

        return; // Success - exit the retry loop
      } else {
        throw new Error(`Auth state file was not created at ${fullPath}`);
      }
    } catch (error) {
      console.error(`❌ Attempt ${attempt + 1} failed for ${role}:`, error);

      if (attempt === maxRetries) {
        console.error(`❌ All ${maxRetries + 1} attempts failed for ${role}. Skipping this role.`);
        results.failed++;
      }
    }
  }
}

// Run the function and handle results appropriately
createAuthStates()
  .then((results) => {
    if (results.failed > 0) {
      console.warn(`⚠️ Warning: ${results.failed} role(s) failed to create auth states.`);
      if (results.success > 0) {
        console.log(`✅ However, ${results.success} role(s) succeeded, including: ${results.roles.join(', ')}`);
        console.log('Continuing with available auth states...');
        // Exit with success if at least Admin role was created
        if (results.roles.includes('Admin')) {
          console.log('Admin role auth state was created successfully.');
          process.exit(0);
        } else {
          console.error('Critical admin role auth state failed to create.');
          process.exit(1);
        }
      } else {
        console.error('❌ All roles failed. Check credentials and application state.');
        process.exit(1);
      }
    } else {
      console.log('✅ All authentication states created successfully!');
      process.exit(0);
    }
  })
  .catch((error) => {
    console.error('❌ Fatal error creating authentication states:', error);
    process.exit(1);
  });
