"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.types = exports.traverse = exports.declare = exports.codeFrameColumns = exports.babelTransform = exports.babelParse = void 0;
/**
 * Copyright (c) Microsoft Corporation.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

const codeFrameColumns = exports.codeFrameColumns = require('./babelBundleImpl').codeFrameColumns;
const declare = exports.declare = require('./babelBundleImpl').declare;
const types = exports.types = require('./babelBundleImpl').types;
const traverse = exports.traverse = require('./babelBundleImpl').traverse;
const babelTransform = exports.babelTransform = require('./babelBundleImpl').babelTransform;
const babelParse = exports.babelParse = require('./babelBundleImpl').babelParse;