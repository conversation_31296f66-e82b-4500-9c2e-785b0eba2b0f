"use strict";
/**
 * CLI Arguments Helper
 *
 * This utility parses command line arguments for scripts.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.getArgs = getArgs;
/**
 * Gets the command line arguments as an object
 */
function getArgs() {
    const args = {};
    // Start from index 2 to skip 'node' and the script name
    for (let i = 2; i < process.argv.length; i++) {
        const arg = process.argv[i];
        // Handle --flag format
        if (arg.startsWith('--')) {
            const flag = arg.substring(2);
            // Handle --flag=value format
            if (flag.includes('=')) {
                const [key, value] = flag.split('=');
                args[key] = value;
            }
            // Handle --flag format (boolean true)
            else {
                args[flag] = true;
            }
        }
    }
    return args;
}
