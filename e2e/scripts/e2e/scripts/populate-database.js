"use strict";
// Database Population Script with Debugging and Verification
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __rest = (this && this.__rest) || function (s, e) {
    var t = {};
    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)
        t[p] = s[p];
    if (s != null && typeof Object.getOwnPropertySymbols === "function")
        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {
            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))
                t[p[i]] = s[p[i]];
        }
    return t;
};
Object.defineProperty(exports, "__esModule", { value: true });
const admin = require("firebase-admin");
const fs = require("fs");
const path = require("path");
const uuid_1 = require("uuid");
const cli_args_1 = require("./utils/cli-args");
const Role_1 = require("../../src/RBAC/[types]/Role");
// --- Ensure test-user-uids.json is present and valid ---
const uidMapPath = path.join(process.cwd(), 'e2e/scripts/test-user-uids.json');
if (!fs.existsSync(uidMapPath)) {
    throw new Error('Missing test-user-uids.json. Please run create-test-users.ts first.');
}
const uidMap = JSON.parse(fs.readFileSync(uidMapPath, 'utf8'));
if (!uidMap.Admin || !uidMap.ClinicAdmin || !uidMap.Doctor || !uidMap.Client || !uidMap.Patient) {
    throw new Error('Incomplete UID map in test-user-uids.json. Please ensure create-test-users.ts ran successfully.');
}
// Load service account if needed for authentication
let serviceAccount;
try {
    const serviceAccountPath = path.resolve(process.cwd(), 'service-account.json');
    if (fs.existsSync(serviceAccountPath)) {
        serviceAccount = require(serviceAccountPath);
    }
}
catch (error) {
    console.warn('Service account file not found. Continuing without it.');
}
// Initialize Firebase Admin
if (!admin.apps.length) {
    if (serviceAccount) {
        admin.initializeApp({
            credential: admin.credential.cert(serviceAccount)
        });
    }
    else {
        admin.initializeApp();
    }
}
// Get Firestore instance
const db = admin.firestore();
// Command line arguments
const args = (0, cli_args_1.getArgs)();
const collectionsToProcess = typeof args.collections === 'string' ? args.collections.split(',') : null;
const clearOnly = args.clearOnly || false;
const noClear = args.noClear || false;
// Define collection names based on the schema
const COLLECTIONS = {
    USERS: 'users',
    CLINICS: 'clinics',
    DOCTORS: 'doctors',
    CLINIC_ADMINS: 'clinicAdmins',
    ASSISTANTS: 'assistants',
    CLIENTS: 'clients',
    PATIENTS: 'patients',
    QUESTIONNAIRE_TEMPLATES: 'questionnaireTemplates',
    QUESTIONNAIRES: 'questionnaires',
    COMPLIANCE_REPORTS: 'complianceReports',
    PURCHASES: 'purchases'
};
// All collections in order of dependency
const ALL_COLLECTIONS = [
    COLLECTIONS.USERS,
    COLLECTIONS.CLINICS,
    COLLECTIONS.DOCTORS,
    COLLECTIONS.CLINIC_ADMINS,
    COLLECTIONS.ASSISTANTS,
    COLLECTIONS.CLIENTS,
    COLLECTIONS.PATIENTS,
    COLLECTIONS.QUESTIONNAIRE_TEMPLATES,
    COLLECTIONS.QUESTIONNAIRES,
    COLLECTIONS.COMPLIANCE_REPORTS,
    COLLECTIONS.PURCHASES
];
// Filter collections based on command line arguments
const collections = collectionsToProcess ? ALL_COLLECTIONS.filter((c) => collectionsToProcess.includes(c)) : ALL_COLLECTIONS;
// Timestamp helpers
function now() {
    return admin.firestore.Timestamp.now();
}
function timestamp(date) {
    return admin.firestore.Timestamp.fromDate(date);
}
// Generate test data with real UIDs/emails
function generateTestData() {
    // Users for each role, using Auth UIDs
    const users = [
        {
            id: uidMap.Admin.uid,
            email: uidMap.Admin.email,
            displayName: 'Test Admin',
            role: Role_1.Role.Admin
        },
        {
            id: uidMap.ClinicAdmin.uid,
            email: uidMap.ClinicAdmin.email,
            displayName: 'Test Clinic Admin',
            role: Role_1.Role.ClinicAdmin
        },
        {
            id: uidMap.Doctor.uid,
            email: uidMap.Doctor.email,
            displayName: 'Test Doctor',
            role: Role_1.Role.Doctor
        },
        {
            id: uidMap.Client.uid,
            email: uidMap.Client.email,
            displayName: 'Test Client',
            role: Role_1.Role.Client
        },
        {
            id: uidMap.Patient.uid,
            email: uidMap.Patient.email,
            displayName: 'Test Patient',
            role: Role_1.Role.Patient
        }
    ];
    // Example clinics
    const clinicIds = {
        testClinic: 'test-clinic-' + (0, uuid_1.v4)().substring(0, 8),
        clinic1: 'clinic1-' + (0, uuid_1.v4)().substring(0, 8)
    };
    const clinics = [
        {
            id: clinicIds.testClinic,
            name: 'Test Clinic',
            address: '100 Test Ave, Testville, USA',
            createdAt: now()
        },
        {
            id: clinicIds.clinic1,
            name: 'Main Street Medical Center',
            address: '123 Main St, Anytown, USA',
            createdAt: now()
        }
    ];
    // Example: clients referencing patient UID
    const clients = [
        {
            id: uidMap.Client.uid,
            email: uidMap.Client.email,
            patientId: uidMap.Patient.uid,
            displayName: 'Test Client',
            role: Role_1.Role.Client
        }
    ];
    // Example: purchases referencing client and patient UIDs
    const purchases = [
        {
            id: 'test-purchase-' + (0, uuid_1.v4)().substring(0, 8),
            clientId: uidMap.Client.uid,
            patientId: uidMap.Patient.uid,
            templateId: 'template1-' + (0, uuid_1.v4)().substring(0, 8),
            questionnaireQuantity: 10,
            unitPrice: 100.0,
            totalPrice: 1000.0,
            isPaid: true,
            date: timestamp(new Date(Date.now() - 5 * 24 * 60 * 60 * 1000)),
            createdAt: now(),
            label: 'Test Purchase'
        }
    ];
    // Add more test data as needed for other collections and relationships...
    return {
        [COLLECTIONS.USERS]: users,
        [COLLECTIONS.CLINICS]: clinics,
        [COLLECTIONS.CLIENTS]: clients,
        [COLLECTIONS.PURCHASES]: purchases
        // Add other collections as needed
    };
}
// Debug: Log document count for a collection
function logCollectionCount(collectionName) {
    return __awaiter(this, void 0, void 0, function* () {
        const snapshot = yield db.collection(collectionName).get();
        console.log(`  [VERIFY] ${collectionName}: ${snapshot.size} documents`);
    });
}
// Populate a collection with test data
function populateCollection(collectionName, data) {
    return __awaiter(this, void 0, void 0, function* () {
        console.log(`Populating collection: ${collectionName} with ${data.length} documents`);
        const collectionRef = db.collection(collectionName);
        const batchSize = 500;
        let batchCount = 0;
        let docCount = 0;
        let batch = db.batch();
        for (const item of data) {
            const { id } = item, itemData = __rest(item, ["id"]);
            const docRef = collectionRef.doc(id);
            batch.set(docRef, itemData);
            batchCount++;
            docCount++;
            if (batchCount >= batchSize) {
                yield batch.commit();
                console.log(`  Added batch of ${batchCount} documents`);
                batch = db.batch();
                batchCount = 0;
            }
        }
        if (batchCount > 0) {
            yield batch.commit();
            console.log(`  Added batch of ${batchCount} documents`);
        }
        console.log(`✅ Added ${docCount} documents to ${collectionName}`);
    });
}
// Populate all specified collections, with debug sections
function populateCollections(collections, testData) {
    return __awaiter(this, void 0, void 0, function* () {
        console.log('=== POPULATING COLLECTIONS ===');
        // Test Clinics
        if (testData[COLLECTIONS.CLINICS]) {
            console.log('\n--- Populating Test Clinics ---');
            yield populateCollection(COLLECTIONS.CLINICS, testData[COLLECTIONS.CLINICS]);
            yield logCollectionCount(COLLECTIONS.CLINICS);
        }
        // Test Users
        if (testData[COLLECTIONS.USERS]) {
            console.log('\n--- Populating Test Users ---');
            yield populateCollection(COLLECTIONS.USERS, testData[COLLECTIONS.USERS]);
            yield logCollectionCount(COLLECTIONS.USERS);
        }
        // Test Clients
        if (testData[COLLECTIONS.CLIENTS]) {
            console.log('\n--- Populating Test Clients ---');
            yield populateCollection(COLLECTIONS.CLIENTS, testData[COLLECTIONS.CLIENTS]);
            yield logCollectionCount(COLLECTIONS.CLIENTS);
        }
        // Test Purchases
        if (testData[COLLECTIONS.PURCHASES]) {
            console.log('\n--- Populating Test Purchases ---');
            yield populateCollection(COLLECTIONS.PURCHASES, testData[COLLECTIONS.PURCHASES]);
            yield logCollectionCount(COLLECTIONS.PURCHASES);
        }
        // Add more debug sections for other collections as needed...
        console.log('\n✅ All specified collections populated and verified.');
    });
}
// Main function to run the script
function run() {
    return __awaiter(this, void 0, void 0, function* () {
        console.log('Database Population Script (Debug Mode)');
        console.log('-------------------------');
        console.log(`Collections to process: ${collections.join(', ')}`);
        try {
            // Clear collections if not skipped
            if (!noClear) {
                console.log('\n=== CLEARING COLLECTIONS ===');
                const reversedCollections = [...collections].reverse();
                for (const collection of reversedCollections) {
                    yield clearCollection(collection);
                    yield logCollectionCount(collection);
                }
                console.log('✅ All specified collections cleared');
            }
            // Populate collections if not clear-only
            if (!clearOnly) {
                const testData = generateTestData();
                yield populateCollections(collections, testData);
            }
            console.log('\n✅ Database population completed successfully');
            process.exit(0);
        }
        catch (error) {
            console.error('❌ Error during database population:', error);
            process.exit(1);
        }
    });
}
// Clear all documents in a collection
function clearCollection(collectionName) {
    return __awaiter(this, void 0, void 0, function* () {
        console.log(`Clearing collection: ${collectionName}`);
        const collectionRef = db.collection(collectionName);
        const batchSize = 500;
        let docsDeleted = 0;
        try {
            const snapshot = yield collectionRef.limit(batchSize).get();
            if (snapshot.empty) {
                console.log(`  Collection ${collectionName} is already empty`);
                return;
            }
            let batch = db.batch();
            let batchCount = 0;
            for (const doc of snapshot.docs) {
                batch.delete(doc.ref);
                batchCount++;
                docsDeleted++;
                if (batchCount >= batchSize) {
                    yield batch.commit();
                    console.log(`  Deleted batch of ${batchCount} documents`);
                    batch = db.batch();
                    batchCount = 0;
                }
            }
            if (batchCount > 0) {
                yield batch.commit();
                console.log(`  Deleted batch of ${batchCount} documents`);
            }
            if (docsDeleted > 0) {
                yield clearCollection(collectionName);
            }
        }
        catch (error) {
            console.error(`❌ Error clearing collection ${collectionName}:`, error);
            throw error;
        }
    });
}
// Run the script
run().catch((error) => {
    console.error('Unhandled error:', error);
    process.exit(1);
});
