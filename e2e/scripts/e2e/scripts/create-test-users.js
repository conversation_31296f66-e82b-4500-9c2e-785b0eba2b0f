"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
const fs = require("fs");
const path = require("path");
// @ts-ignore
const admin = require('firebase-admin');
const dotenv = require("dotenv");
const Role_1 = require("../../src/RBAC/[types]/Role");
// Load environment variables
dotenv.config();
// Load service account if needed for authentication
let serviceAccount;
try {
    const serviceAccountPath = path.resolve(process.cwd(), 'service-account.json');
    if (fs.existsSync(serviceAccountPath)) {
        serviceAccount = require(serviceAccountPath);
    }
}
catch (error) {
    console.warn('Service account file not found. Continuing without it.');
}
// Initialize Firebase Admin
if (!admin.apps.length) {
    if (serviceAccount) {
        admin.initializeApp({
            credential: admin.credential.cert(serviceAccount)
        });
    }
    else {
        admin.initializeApp();
    }
}
/**
 * This script creates test users for e2e tests using Firebase Admin SDK
 * These users can then be used by the create-auth-states.ts script
 */
function createTestUsers() {
    return __awaiter(this, void 0, void 0, function* () {
        var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k, _l;
        console.log('Creating test users for e2e testing...');
        // Ensure the environment variables directory exists
        const envDir = path.join(process.cwd(), 'e2e', 'config');
        if (!fs.existsSync(envDir)) {
            console.log(`Creating config directory: ${envDir}`);
            fs.mkdirSync(envDir, { recursive: true });
        }
        // Define test users with standardized credentials and TRQUser fields
        const testUsers = [
            {
                email: '<EMAIL>',
                password: 'password123',
                displayName: 'Test Admin',
                role: Role_1.Role.Admin,
                firstName: 'Test',
                lastName: 'Admin',
                phone: '555-0001',
                title: 'System Administrator',
                company: 'Test Health Org',
                userName: 'testadmin',
                isActive: true,
                address: '123 Admin St',
                city: 'Admin City',
                state: 'CA',
                zipCode: '90001',
                country: 'USA',
                createdAt: new Date(),
                updatedAt: new Date(),
                metadata: { note: 'E2E test admin user' }
            },
            {
                email: '<EMAIL>',
                password: 'password123',
                displayName: 'Test Clinic Admin',
                role: Role_1.Role.ClinicAdmin,
                firstName: 'Test',
                lastName: 'ClinicAdmin',
                phone: '555-0002',
                title: 'Clinic Manager',
                company: 'Test Health Org',
                userName: 'testclinicadmin',
                isActive: true,
                address: '456 Clinic Rd',
                city: 'Clinic City',
                state: 'CA',
                zipCode: '90002',
                country: 'USA',
                createdAt: new Date(),
                updatedAt: new Date(),
                metadata: { note: 'E2E test clinic admin user' },
                asClinicAdmin: { managedClinics: ['clinic-001'] }
            },
            {
                email: '<EMAIL>',
                password: 'password123',
                displayName: 'Test Doctor',
                role: Role_1.Role.Doctor,
                firstName: 'Test',
                lastName: 'Doctor',
                phone: '555-0003',
                title: 'MD',
                company: 'Test Health Org',
                userName: 'testdoctor',
                isActive: true,
                address: '789 Doctor Ave',
                city: 'Doctor City',
                state: 'CA',
                zipCode: '90003',
                country: 'USA',
                createdAt: new Date(),
                updatedAt: new Date(),
                metadata: { note: 'E2E test doctor user' },
                asDoctor: { specialties: ['Pulmonology'] }
            },
            {
                email: '<EMAIL>',
                password: 'password123',
                displayName: 'Test Client',
                role: Role_1.Role.Client,
                firstName: 'Test',
                lastName: 'Client',
                phone: '555-0004',
                title: 'Manager',
                company: 'Test Health Org',
                userName: 'testclient',
                isActive: true,
                address: '321 Client Blvd',
                city: 'Client City',
                state: 'CA',
                zipCode: '90004',
                country: 'USA',
                createdAt: new Date(),
                updatedAt: new Date(),
                metadata: { note: 'E2E test client user' },
                asClient: { associatedPatients: [] }
            },
            {
                email: '<EMAIL>',
                password: 'password123',
                displayName: 'Test Patient',
                role: Role_1.Role.Patient,
                firstName: 'Test',
                lastName: 'Patient',
                phone: '555-0005',
                title: 'Patient',
                company: 'Test Health Org',
                userName: 'testpatient',
                isActive: true,
                address: '654 Patient Ln',
                city: 'Patient City',
                state: 'CA',
                zipCode: '90005',
                country: 'USA',
                createdAt: new Date(),
                updatedAt: new Date(),
                metadata: { note: 'E2E test patient user' },
                asPatient: { allergies: ['None'], medications: ['None'] }
            }
        ];
        // Create each test user
        const results = {
            created: [],
            existing: [],
            updated: [],
            failed: []
        };
        // Map to store role/email to UID
        const uidMap = {};
        for (const user of testUsers) {
            try {
                console.log(`Creating/updating user for role: ${user.role}...`);
                // Check if user exists
                let existingUser;
                try {
                    existingUser = yield admin.auth().getUserByEmail(user.email);
                }
                catch (err) {
                    // User does not exist
                }
                let uid;
                if (existingUser) {
                    // Update user password and displayName if needed
                    yield admin.auth().updateUser(existingUser.uid, {
                        password: user.password,
                        displayName: user.displayName
                    });
                    uid = existingUser.uid;
                    console.log(`✅ Updated user ${user.email}`);
                    results.updated.push(user.email);
                    results.existing.push(user.email);
                }
                else {
                    // Create new user
                    const created = yield admin.auth().createUser({
                        email: user.email,
                        password: user.password,
                        displayName: user.displayName
                    });
                    uid = created.uid;
                    console.log(`✅ Created user ${user.email}`);
                    results.created.push(user.email);
                }
                // Store UID mapping
                uidMap[user.role] = { email: user.email, uid, role: user.role };
                // Add user to Firestore 'users' collection (TRQUser shape)
                try {
                    const trqUser = Object.assign(Object.assign({}, user), { id: uid, uid, createdAt: new Date(), updatedAt: new Date() });
                    yield admin.firestore().collection('users').doc(uid).set(trqUser, { merge: true });
                    console.log(`📄 Added/updated Firestore user document for ${user.email}`);
                }
                catch (firestoreError) {
                    console.error(`❌ Failed to add Firestore user document for ${user.email}:`, firestoreError);
                    results.failed.push(user.email + ' (firestore)');
                }
                // Update the .env file with the test user credentials
                updateEnvironmentVariable(`E2E_${user.role.toUpperCase()}_EMAIL`, user.email);
                updateEnvironmentVariable(`E2E_${user.role.toUpperCase()}_PASSWORD`, user.password);
            }
            catch (error) {
                console.error(`❌ Failed to create/update user for role ${user.role}:`, error);
                results.failed.push(user.email);
            }
        }
        // Write UID map to file for Firestore population script
        const uidMapPath = path.join(process.cwd(), 'e2e/scripts/test-user-uids.json');
        fs.writeFileSync(uidMapPath, JSON.stringify(uidMap, null, 2));
        console.log(`📝 Test user UID map written to: ${uidMapPath}`);
        // --- After user creation, add templates, purchases, and questionnaires ---
        // 1. Create Questionnaire Templates
        const template1Id = 'template-1-' + Math.random().toString(36).substring(2, 8);
        const template2Id = 'template-2-' + Math.random().toString(36).substring(2, 8);
        const now = new Date();
        const templates = [
            {
                id: template1Id,
                name: 'Respiratory Assessment',
                title: 'Respiratory Assessment',
                createdBy: (_a = uidMap.Doctor) === null || _a === void 0 ? void 0 : _a.uid,
                description: 'A standard respiratory health assessment.',
                isPublished: true,
                metadata: { category: 'Respiratory', estimatedTime: 15 },
                questions: [
                    { id: 'q1', text: 'Do you experience shortness of breath?', type: 'boolean' },
                    { id: 'q2', text: 'How often do you use your inhaler?', type: 'text' }
                ],
                createdAt: now,
                updatedAt: now,
                version: 1,
                category: 'Respiratory',
                tags: ['respiratory', 'assessment']
            },
            {
                id: template2Id,
                name: 'Asthma Control',
                title: 'Asthma Control Questionnaire',
                createdBy: (_b = uidMap.Doctor) === null || _b === void 0 ? void 0 : _b.uid,
                description: 'Asthma control follow-up.',
                isPublished: true,
                metadata: { category: 'Asthma', estimatedTime: 10 },
                questions: [
                    { id: 'q1', text: 'Have you had any asthma attacks this month?', type: 'boolean' },
                    { id: 'q2', text: 'Rate your asthma control (1-10)', type: 'number' }
                ],
                createdAt: now,
                updatedAt: now,
                version: 1,
                category: 'Asthma',
                tags: ['asthma', 'followup']
            }
        ];
        for (const t of templates) {
            yield admin.firestore().collection('questionnaireTemplates').doc(t.id).set(t);
        }
        // 2. Create a Purchase for the Client
        const purchaseId = 'purchase-' + Math.random().toString(36).substring(2, 8);
        const purchase = {
            id: purchaseId,
            clientId: (_c = uidMap.Client) === null || _c === void 0 ? void 0 : _c.uid,
            clientName: 'Test Client',
            questionnaires: [
                {
                    id: template1Id,
                    name: 'Respiratory Assessment',
                    quantity: 5,
                    unitPrice: 100
                }
            ],
            totalQuantity: 5,
            totalPrice: 500,
            paymentStatus: 'paid',
            purchaseDate: now,
            lastUpdated: now,
            paymentMethod: 'credit_card',
            transactionId: 'txn-' + Math.random().toString(36).substring(2, 8),
            notes: 'Test purchase',
            templateId: template1Id,
            questionnaireQuantity: 5,
            unitPrice: 100,
            subTotal: 500,
            tax: 0,
            isPaid: true,
            date: now,
            paymentType: 'credit_card'
        };
        yield admin.firestore().collection('purchases').doc(purchaseId).set(purchase);
        // 3. Create a Questionnaire for the Patient
        const questionnaireId = 'questionnaire-' + Math.random().toString(36).substring(2, 8);
        const questionnaire = {
            id: questionnaireId,
            name: 'Respiratory Assessment',
            templateId: template1Id,
            patientId: (_d = uidMap.Patient) === null || _d === void 0 ? void 0 : _d.uid,
            clientId: (_e = uidMap.Client) === null || _e === void 0 ? void 0 : _e.uid,
            assignedDoctorId: (_f = uidMap.Doctor) === null || _f === void 0 ? void 0 : _f.uid,
            doctorId: (_g = uidMap.Doctor) === null || _g === void 0 ? void 0 : _g.uid,
            clinicId: null,
            status: 'assigned',
            hashCode: Math.random().toString(36).substring(2, 12),
            savedSection: 1,
            startedAt: now,
            completedAt: null,
            isReviewed: false,
            complianceReportId: null,
            title: 'Respiratory Assessment',
            description: 'A standard respiratory health assessment.',
            questions: templates[0].questions,
            responses: [],
            score: null,
            reviewedAt: null,
            reviewedBy: null,
            metadata: { note: 'E2E test questionnaire' },
            createdAt: now,
            updatedAt: now,
            comments: [],
            createdBy: (_h = uidMap.Doctor) === null || _h === void 0 ? void 0 : _h.uid,
            category: 'Respiratory',
            tags: ['respiratory', 'assessment'],
            template: templates[0],
            assignedTo: {
                patientId: (_j = uidMap.Patient) === null || _j === void 0 ? void 0 : _j.uid,
                patientUid: (_k = uidMap.Patient) === null || _k === void 0 ? void 0 : _k.uid,
                assignedAt: now,
                assignedBy: (_l = uidMap.Doctor) === null || _l === void 0 ? void 0 : _l.uid
            }
        };
        yield admin.firestore().collection('questionnaires').doc(questionnaireId).set(questionnaire);
        // Output summary
        console.log('\n========== Test User Creation Summary ==========');
        console.log(`Created: ${results.created.length} (${results.created.join(', ')})`);
        console.log(`Updated: ${results.updated.length} (${results.updated.join(', ')})`);
        console.log(`Existing: ${results.existing.length} (${results.existing.join(', ')})`);
        console.log(`Failed: ${results.failed.length} (${results.failed.length > 0 ? results.failed.join(', ') : 'None'})`);
        console.log('================================================\n');
        // Generate .env file for e2e tests with all the test user credentials
        generateEnvFile(testUsers);
        return results;
    });
}
/**
 * Updates an environment variable in the .env file
 */
function updateEnvironmentVariable(key, value) {
    const envFile = path.join(process.cwd(), '.env');
    let envContent = '';
    if (fs.existsSync(envFile)) {
        envContent = fs.readFileSync(envFile, 'utf8');
    }
    // Check if the key already exists
    const regex = new RegExp(`^${key}=.*`, 'm');
    if (regex.test(envContent)) {
        // Update existing key
        envContent = envContent.replace(regex, `${key}=${value}`);
    }
    else {
        // Add new key
        envContent += `\n${key}=${value}`;
    }
    // Write back to the file
    fs.writeFileSync(envFile, envContent);
}
/**
 * Generates a .env file specifically for e2e tests
 */
function generateEnvFile(users) {
    const envFilePath = path.join(process.cwd(), 'e2e', '.env');
    let envContent = '# E2E Test Environment Variables\n# Generated on ' + new Date().toISOString() + '\n\n';
    for (const user of users) {
        envContent += `E2E_${user.role.toUpperCase()}_EMAIL=${user.email}\n`;
        envContent += `E2E_${user.role.toUpperCase()}_PASSWORD=${user.password}\n\n`;
    }
    fs.writeFileSync(envFilePath, envContent);
    console.log(`📝 E2E environment file created at: ${envFilePath}`);
}
// Run the function
createTestUsers()
    .then(() => {
    console.log('✅ Test user creation process completed.');
    console.log('You can now run the create-auth-states.ts script to generate auth states for these users.');
    process.exit(0);
})
    .catch((error) => {
    console.error('❌ Fatal error creating test users:', error);
    process.exit(1);
});
