import { chromium, devices } from '@playwright/test';
import fs from 'fs';
import path from 'path';
import dotenv from 'dotenv';
import { LoginPage } from '../[tests]/Authentication/pageObjects/login.page';
import { Role } from '../../src/RBAC/[types]/Role';

// ES Modules equivalent of __dirname for dotenv
const __dotenvFilename = new URL(import.meta.url).pathname;
const __dotenvDirname = path.dirname(__dotenvFilename);

// Load environment variables
dotenv.config({ path: path.join(__dotenvDirname, '../.env') });

interface RouteInfo {
  path: string;
  name: string;
  module: string;
  isDynamic: boolean;
  sampleParams?: Record<string, string>;
  requiresAuth: boolean;
  resolvedPath?: string;
  roles?: string[];
}

interface ScreenshotInfo {
  routeName: string;
  path: string;
  viewportSize: string;
  screenshotPath: string;
  timestamp: string;
  role: string;
}

// ES Modules equivalent of __dirname for configuration
const __configFilename = new URL(import.meta.url).pathname;
const __configDirname = path.dirname(__configFilename);

// Configuration
const config = {
  baseUrl: process.env.BASE_URL || 'http://localhost:3001',
  outputDir: path.join(__configDirname, '../ui-audit/screenshots'),
  authCredentials: {
    admin: { email: process.env.E2E_ADMIN_EMAIL || '<EMAIL>', password: process.env.E2E_ADMIN_PASSWORD || 'password123' },
    doctor: { email: process.env.E2E_DOCTOR_EMAIL || '<EMAIL>', password: process.env.E2E_DOCTOR_PASSWORD || 'password123' },
    patient: { email: process.env.E2E_PATIENT_EMAIL || '<EMAIL>', password: process.env.E2E_PATIENT_PASSWORD || 'password123' },
    client: { email: process.env.E2E_CLIENT_EMAIL || '<EMAIL>', password: process.env.E2E_CLIENT_PASSWORD || 'password123' },
    clinicAdmin: { email: process.env.E2E_CLINIC_ADMIN_EMAIL || '<EMAIL>', password: process.env.E2E_CLINIC_ADMIN_PASSWORD || 'password123' }
  },
  viewportSizes: {
    desktop: { width: 1920, height: 1080 },
    tablet: { width: 768, height: 1024 },
    mobile: { width: 375, height: 667 }
  },
  maxScreenshotsPerRole: 50, // For testing purposes, limit the number of screenshots
  screenshotDelay: 1000 // Time to wait after page load before taking screenshot (ms)
};

// Main function to take screenshots of all routes
async function takeScreenshots() {
  // ES Modules equivalent of __dirname
  const __filename = new URL(import.meta.url).pathname;
  const __dirname = path.dirname(__filename);

  // Read route catalog
  const routeCatalogPath = path.join(__dirname, '../ui-audit/route-catalog.json');
  if (!fs.existsSync(routeCatalogPath)) {
    console.error('Route catalog not found. Please run the extract-routes script first.');
    process.exit(1);
  }

  const routeCatalog: RouteInfo[] = JSON.parse(
    fs.readFileSync(routeCatalogPath, 'utf8')
  );

  // Prepare output directory
  if (!fs.existsSync(config.outputDir)) {
    fs.mkdirSync(config.outputDir, { recursive: true });
  }

  const screenshotManifest: ScreenshotInfo[] = [];
  const timestamp = new Date().toISOString().replace(/:/g, '-');

  // Launch browser for unauthenticated routes
  console.log('\n===== Taking screenshots for public routes =====');
  const browser = await chromium.launch();
  const context = await browser.newContext(devices['Desktop Chrome']);
  const page = await context.newPage();

  try {
    // Take screenshots for each viewport size
    for (const [sizeName, viewport] of Object.entries(config.viewportSizes)) {
      console.log(`\nUsing viewport: ${sizeName} (${viewport.width}x${viewport.height})`);
      await page.setViewportSize(viewport);

      // Find all public routes (not requiring authentication)
      const publicRoutes = routeCatalog.filter(route => !route.requiresAuth);
      console.log(`Found ${publicRoutes.length} public routes to capture`);

      let screenshotCount = 0;
      for (const route of publicRoutes) {
        // Skip if we've reached the limit for testing
        if (screenshotCount >= config.maxScreenshotsPerRole) {
          console.log(`Reached max screenshots limit`);
          break;
        }

        // Use resolvedPath for dynamic routes or regular path for static routes
        const routePath = route.resolvedPath || route.path;
        const url = `${config.baseUrl}${routePath}`;

        try {
          // Navigate to the route
          console.log(`Navigating to: ${url}`);
          await page.goto(url, { timeout: 30000, waitUntil: 'networkidle' });

          // Wait a moment for any animations or lazy-loaded content
          await page.waitForTimeout(config.screenshotDelay);

          // Create directory structure based on module and route name
          const moduleDir = route.module.toLowerCase().replace(/\./g, '-');
          const routeDir = route.name.toLowerCase().replace(/\./g, '-');
          const screenshotDir = path.join(
            config.outputDir,
            moduleDir,
            routeDir
          );

          if (!fs.existsSync(screenshotDir)) {
            fs.mkdirSync(screenshotDir, { recursive: true });
          }

          // Take screenshot
          const screenshotFileName = `public-${sizeName}-${timestamp}.png`;
          const screenshotPath = path.join(screenshotDir, screenshotFileName);

          await page.screenshot({
            path: screenshotPath,
            fullPage: true
          });

          // Add to manifest
          screenshotManifest.push({
            routeName: route.name,
            path: routePath,
            viewportSize: sizeName,
            screenshotPath: path.relative(config.outputDir, screenshotPath),
            timestamp,
            role: 'public'
          });

          console.log(`Screenshot saved: ${path.relative(process.cwd(), screenshotPath)}`);
          screenshotCount++;
        } catch (error: any) {
          console.error(`Error capturing screenshot for ${url}:`, error.message || String(error));
        }
      }
    }
  } catch (error: any) {
    console.error(`Error taking screenshots:`, error.message || String(error));
  } finally {
    await context.close();
    await browser.close();
  }

  // Save manifest
  const manifestPath = path.join(config.outputDir, `manifest-${timestamp}.json`);
  fs.writeFileSync(
    manifestPath,
    JSON.stringify(screenshotManifest, null, 2)
  );

  console.log(`\nScreenshot process complete. ${screenshotManifest.length} screenshots taken.`);
  console.log(`Screenshot manifest saved to: ${path.relative(process.cwd(), manifestPath)}`);
}

// Run the main function
takeScreenshots().catch((error: any) => {
  console.error('Fatal error:', error.message || String(error));
  process.exit(1);
});
