// Database Population Script with Debugging and Verification

import * as admin from 'firebase-admin';
import * as fs from 'fs';
import * as path from 'path';
import { v4 as uuidv4 } from 'uuid';
import { getArgs } from './cli-args';
import { Role } from '../../src/RBAC/[types]/Role';
import chalk from 'chalk';

// --- Ensure test-user-uids.json is present and valid ---
const uidMapPath = path.join(process.cwd(), 'e2e/scripts/test-user-uids.json');
if (!fs.existsSync(uidMapPath)) {
  throw new Error('Missing test-user-uids.json. Please run create-test-users.ts first.');
}
const uidMap = JSON.parse(fs.readFileSync(uidMapPath, 'utf8'));
if (!uidMap.Admin || !uidMap.ClinicAdmin || !uidMap.Doctor || !uidMap.Client || !uidMap.Patient) {
  throw new Error('Incomplete UID map in test-user-uids.json. Please ensure create-test-users.ts ran successfully.');
}

// Load service account if needed for authentication
let serviceAccount: any;
try {
  const serviceAccountPath = path.resolve(process.cwd(), 'service-account.json');
  if (fs.existsSync(serviceAccountPath)) {
    serviceAccount = require(serviceAccountPath);
  }
} catch (error) {
  console.warn('Service account file not found. Continuing without it.');
}

// Initialize Firebase Admin
if (!admin.apps.length) {
  if (serviceAccount) {
    admin.initializeApp({
      credential: admin.credential.cert(serviceAccount)
    });
  } else {
    admin.initializeApp();
  }
}

// Get Firestore instance
const db = admin.firestore();

// Command line arguments
const args = getArgs();
const collectionsToProcess = typeof args.collections === 'string' ? args.collections.split(',') : null;
const clearOnly = args.clearOnly || false;
const noClear = args.noClear || false;

// Define collection names based on the schema
const COLLECTIONS = {
  USERS: 'users',
  CLINICS: 'clinics',
  DOCTORS: 'doctors',
  CLINIC_ADMINS: 'clinicAdmins',
  ASSISTANTS: 'assistants',
  QUESTIONNAIRE_TEMPLATES: 'questionnaireTemplates',
  QUESTIONNAIRES: 'questionnaires',
  COMPLIANCE_REPORTS: 'complianceReports',
  PURCHASES: 'purchases',
  PRODUCTS: 'products'
};

// All collections in order of dependency
const ALL_COLLECTIONS = [
  COLLECTIONS.USERS,
  COLLECTIONS.CLINICS,
  COLLECTIONS.DOCTORS,
  COLLECTIONS.CLINIC_ADMINS,
  COLLECTIONS.ASSISTANTS,
  COLLECTIONS.QUESTIONNAIRE_TEMPLATES,
  COLLECTIONS.QUESTIONNAIRES,
  COLLECTIONS.COMPLIANCE_REPORTS,
  COLLECTIONS.PURCHASES,
  COLLECTIONS.PRODUCTS
];

// Filter collections based on command line arguments
const collections = collectionsToProcess ? ALL_COLLECTIONS.filter((c) => collectionsToProcess.includes(c)) : ALL_COLLECTIONS;

// Timestamp helpers
function now(): admin.firestore.Timestamp {
  return admin.firestore.Timestamp.now();
}
function timestamp(date: Date): admin.firestore.Timestamp {
  return admin.firestore.Timestamp.fromDate(date);
}

// Generate test data with real UIDs/emails
function generateTestData() {
  // Example clinics
  const clinicIds = {
    testClinic: 'test-clinic-' + uuidv4().substring(0, 8),
    clinic1: 'clinic1-' + uuidv4().substring(0, 8)
  };
  const clinics = [
    {
      id: clinicIds.testClinic,
      name: 'Test Clinic',
      address: '100 Test Ave, Testville, USA',
      createdAt: now()
    },
    {
      id: clinicIds.clinic1,
      name: 'Main Street Medical Center',
      address: '123 Main St, Anytown, USA',
      createdAt: now()
    }
  ];

  // Example doctors
  const doctors = [
    {
      uid: uidMap.Doctor.uid,
      email: uidMap.Doctor.email,
      displayName: 'Test Doctor',
      firstName: 'Test',
      lastName: 'Doctor',
      role: Role.Doctor,
      specialization: 'General Practice',
      clinicId: clinicIds.testClinic,
      phone: '555-9000',
      address: '10 Doctor Lane, Testville, USA',
      isActive: true,
      createdAt: now()
    },
    {
      uid: 'doctor2-' + uuidv4().substring(0, 8),
      email: '<EMAIL>',
      displayName: 'Jane Smith',
      firstName: 'Jane',
      lastName: 'Smith',
      role: Role.Doctor,
      specialization: 'Pediatrics',
      clinicId: clinicIds.clinic1,
      phone: '555-9100',
      address: '11 Medical Blvd, Anytown, USA',
      isActive: true,
      createdAt: now()
    }
  ];

  // Add doctor lookup object for easier reference
  const doctorsByClinic: Record<string, string> = {
    [clinicIds.testClinic]: doctors[0].uid,
    [clinicIds.clinic1]: doctors[1].uid
  };

  // Example clients to be assigned to patients
  const clients = [
    {
      uid: uidMap.Client.uid,
      email: uidMap.Client.email,
      displayName: 'Test Client',
      firstName: 'Test',
      lastName: 'Client',
      role: Role.Client,
      companyName: 'Test Company',
      patientUids: [], // Will be populated after creating patients
      createdAt: now(),
      isActive: true,
      phone: '555-4000',
      address: '4 Client Blvd, Testville, USA'
    },
    {
      uid: 'client2-' + uuidv4().substring(0, 8),
      email: '<EMAIL>',
      displayName: 'Family Manager',
      firstName: 'Family',
      lastName: 'Manager',
      role: Role.Client,
      companyName: 'Family Care Services',
      patientUids: [], // Will be populated after creating patients
      createdAt: now(),
      isActive: true,
      phone: '555-5000',
      address: '5 Family Ct, Anytown, USA'
    },
    {
      uid: 'client3-' + uuidv4().substring(0, 8),
      email: '<EMAIL>',
      displayName: 'Caregiver',
      firstName: 'Care',
      lastName: 'Giver',
      role: Role.Client,
      companyName: 'Caregiver Services',
      patientUids: [], // Will be populated after creating patients
      createdAt: now(),
      isActive: true,
      phone: '555-8000',
      address: '8 Caregiver Rd, Testville, USA'
    }
  ];

  // Create a function to assign a client from our pool
  const getClientForPatient = (index: number) => {
    // Assign clients in a round-robin fashion
    return clients[index % clients.length].uid;
  };

  // Example patients with clinic, doctor, and client associations
  const patients = [
    {
      uid: uidMap.Patient.uid,
      email: uidMap.Patient.email,
      displayName: 'Test Patient',
      firstName: 'Test',
      lastName: 'Patient',
      role: Role.Patient,
      clinicId: clinicIds.testClinic,
      doctorId: doctorsByClinic[clinicIds.testClinic],
      clientId: clients[0].uid, // Assigned to first client
      dateOfBirth: '1990-01-01',
      gender: 'female',
      address: '1 Patient Rd, Testville, USA',
      phone: '555-1000',
      insurance: 'Test Insurance',
      isActive: true,
      createdAt: now(),
      asPatient: {
        dateOfBirth: '1990-01-01',
        gender: 'female',
        contactInfo: {
          address: '1 Patient Rd, Testville, USA',
          phone: '555-1000'
        },
        insurance: 'Test Insurance',
        clientId: clients[0].uid,
        doctorId: doctorsByClinic[clinicIds.testClinic]
      }
    },
    {
      uid: 'patient2-' + uuidv4().substring(0, 8),
      email: '<EMAIL>',
      displayName: 'John Doe',
      firstName: 'John',
      lastName: 'Doe',
      role: Role.Patient,
      clinicId: clinicIds.clinic1,
      doctorId: doctorsByClinic[clinicIds.clinic1],
      clientId: clients[1].uid, // Assigned to second client
      dateOfBirth: '1985-05-12',
      gender: 'male',
      address: '2 Patient Ave, Anytown, USA',
      phone: '555-2000',
      insurance: 'SuperHealth',
      isActive: true,
      createdAt: now(),
      asPatient: {
        dateOfBirth: '1985-05-12',
        gender: 'male',
        contactInfo: {
          address: '2 Patient Ave, Anytown, USA',
          phone: '555-2000'
        },
        insurance: 'SuperHealth',
        clientId: clients[1].uid,
        doctorId: doctorsByClinic[clinicIds.clinic1]
      }
    },
    {
      uid: 'patient3-' + uuidv4().substring(0, 8),
      email: '<EMAIL>',
      displayName: 'Susan Smith',
      firstName: 'Susan',
      lastName: 'Smith',
      role: Role.Patient,
      clinicId: clinicIds.testClinic,
      doctorId: doctorsByClinic[clinicIds.testClinic],
      clientId: clients[2].uid, // Assigned to third client
      dateOfBirth: '1978-09-30',
      gender: 'female',
      address: '3 Clinic Rd, Testville, USA',
      phone: '555-3000',
      insurance: 'MediCare',
      isActive: true,
      createdAt: now(),
      asPatient: {
        dateOfBirth: '1978-09-30',
        gender: 'female',
        contactInfo: {
          address: '3 Clinic Rd, Testville, USA',
          phone: '555-3000'
        },
        insurance: 'MediCare',
        clientId: clients[2].uid,
        doctorId: doctorsByClinic[clinicIds.testClinic]
      }
    },
    {
      uid: 'patient4-' + uuidv4().substring(0, 8),
      email: '<EMAIL>',
      displayName: 'Pediatric Patient',
      firstName: 'Pediatric',
      lastName: 'Patient',
      role: Role.Patient,
      clinicId: clinicIds.clinic1,
      doctorId: doctorsByClinic[clinicIds.clinic1],
      clientId: clients[0].uid, // Assigned to first client
      dateOfBirth: '2015-07-20',
      gender: 'male',
      address: '6 Kids Ave, Anytown, USA',
      phone: '555-6000',
      insurance: 'ChildCare',
      isActive: true,
      createdAt: now(),
      asPatient: {
        dateOfBirth: '2015-07-20',
        gender: 'male',
        contactInfo: {
          address: '6 Kids Ave, Anytown, USA',
          phone: '555-6000'
        },
        insurance: 'ChildCare',
        clientId: clients[0].uid,
        doctorId: doctorsByClinic[clinicIds.clinic1]
      }
    },
    {
      uid: 'patient5-' + uuidv4().substring(0, 8),
      email: '<EMAIL>',
      displayName: 'Geriatrics Patient',
      firstName: 'Geriatrics',
      lastName: 'Patient',
      role: Role.Patient,
      clinicId: clinicIds.testClinic,
      doctorId: doctorsByClinic[clinicIds.testClinic],
      clientId: clients[1].uid, // Assigned to second client
      dateOfBirth: '1940-02-15',
      gender: 'female',
      address: '7 Seniors Rd, Testville, USA',
      phone: '555-7000',
      insurance: 'SeniorHealth',
      isActive: true,
      createdAt: now(),
      asPatient: {
        dateOfBirth: '1940-02-15',
        gender: 'female',
        contactInfo: {
          address: '7 Seniors Rd, Testville, USA',
          phone: '555-7000'
        },
        insurance: 'SeniorHealth',
        clientId: clients[1].uid,
        doctorId: doctorsByClinic[clinicIds.testClinic]
      }
    }
  ];

  // Update clients with their associated patients
  clients[0].patientUids = [patients[0].uid, patients[3].uid];
  clients[1].patientUids = [patients[1].uid, patients[4].uid];
  clients[2].patientUids = [patients[2].uid];

  // --- USERS: combine all patients, clients, doctors and standard users ---
  interface UserDoc {
    uid: string;
    email: string;
    displayName: string;
    role: string;
    [key: string]: any;
  }
  // Only include standard auth users (from uidMap) in the users collection
  const users: UserDoc[] = [];
  patients.forEach((p) => users.push(p));
  clients.forEach((c) => users.push(c));
  doctors.forEach((d) => users.push(d));
  if (uidMap.Admin) users.push({ ...uidMap.Admin, role: Role.Admin, displayName: 'Test Admin' });
  if (uidMap.ClinicAdmin) users.push({ ...uidMap.ClinicAdmin, role: Role.ClinicAdmin, displayName: 'Test Clinic Admin' });

  // Example questionnaire templates (variety of types)
  const questionnaireTemplates = [
    {
      id: 'template1-' + uuidv4().substring(0, 8),
      clinicId: clinicIds.testClinic,
      name: 'General Health Assessment',
      description: 'A general health screening questionnaire.',
      sections: [
        { title: 'Vitals', questions: ['Height', 'Weight', 'Blood Pressure'] },
        { title: 'Lifestyle', questions: ['Smoking', 'Alcohol', 'Exercise'] }
      ],
      createdAt: now(),
      isActive: true
    },
    {
      id: 'template2-' + uuidv4().substring(0, 8),
      clinicId: clinicIds.clinic1,
      name: 'Mental Health Survey',
      description: 'Screening for anxiety and depression.',
      sections: [
        { title: 'Mood', questions: ['Feeling down', 'Anxious thoughts'] },
        { title: 'Sleep', questions: ['Sleep quality', 'Insomnia'] }
      ],
      createdAt: now(),
      isActive: true
    },
    {
      id: 'template3-' + uuidv4().substring(0, 8),
      clinicId: clinicIds.clinic1,
      name: 'Pediatric Intake',
      description: 'Initial intake for pediatric patients.',
      sections: [
        { title: 'Development', questions: ['Milestones', 'Vaccinations'] },
        { title: 'Family History', questions: ['Genetic conditions', 'Allergies'] }
      ],
      createdAt: now(),
      isActive: true
    }
  ];

  // Example questionnaires (variety of patients, templates, clinics, statuses)
  const questionnaires = [
    {
      id: 'q1-' + uuidv4().substring(0, 8),
      patientId: patients[0].uid,
      clinicId: clinicIds.testClinic,
      templateId: questionnaireTemplates[0].id,
      status: 'completed',
      responses: {
        Height: '170cm',
        Weight: '70kg',
        'Blood Pressure': '120/80',
        Smoking: 'No',
        Alcohol: 'Occasionally',
        Exercise: '3x/week'
      },
      completedAt: timestamp(new Date(Date.now() - 2 * 24 * 60 * 60 * 1000)),
      createdAt: timestamp(new Date(Date.now() - 5 * 24 * 60 * 60 * 1000))
    },
    {
      id: 'q2-' + uuidv4().substring(0, 8),
      patientId: patients[1].uid,
      clinicId: clinicIds.clinic1,
      templateId: questionnaireTemplates[1].id,
      status: 'pending',
      responses: {},
      createdAt: timestamp(new Date(Date.now() - 3 * 24 * 60 * 60 * 1000))
    },
    {
      id: 'q3-' + uuidv4().substring(0, 8),
      patientId: patients[2].uid,
      clinicId: clinicIds.testClinic,
      templateId: questionnaireTemplates[0].id,
      status: 'in-progress',
      responses: { Height: '160cm', Weight: '', 'Blood Pressure': '', Smoking: '', Alcohol: '', Exercise: '' },
      createdAt: timestamp(new Date(Date.now() - 1 * 24 * 60 * 60 * 1000))
    },
    {
      id: 'q4-' + uuidv4().substring(0, 8),
      patientId: patients[0].uid,
      clinicId: clinicIds.testClinic,
      templateId: questionnaireTemplates[2].id,
      status: 'completed',
      responses: { Milestones: 'Normal', Vaccinations: 'Up to date', 'Genetic conditions': 'None', Allergies: 'Peanuts' },
      completedAt: timestamp(new Date(Date.now() - 4 * 24 * 60 * 60 * 1000)),
      createdAt: timestamp(new Date(Date.now() - 6 * 24 * 60 * 60 * 1000))
    }
  ];

  // Products are published questionnaire templates
  const products = questionnaireTemplates
    .filter((qt) => qt.isActive)
    .map((qt) => ({
      id: qt.id,
      name: qt.name,
      description: qt.description,
      clinicId: qt.clinicId,
      price: Math.floor(Math.random() * 100) + 50, // Random price for demo
      published: true,
      createdAt: qt.createdAt
    }));

  // --- Robustness: Ensure we have enough products for purchases ---
  if (products.length < 3) {
    throw new Error(
      `Not enough products generated from questionnaire templates. Need at least 3, got ${products.length}. Please ensure at least 3 active questionnaire templates.`
    );
  }

  // Purchases = orders of products (questionnaire templates) by clients for patients
  const purchases = [
    products[0] && {
      id: 'test-purchase-' + uuidv4().substring(0, 8),
      clientId: clients[0].uid,
      patientId: patients[0].uid,
      productId: products[0].id,
      templateId: products[0].id,
      questionnaireQuantity: 1,
      unitPrice: products[0].price,
      totalPrice: products[0].price,
      isPaid: true,
      date: timestamp(new Date(Date.now() - 5 * 24 * 60 * 60 * 1000)),
      createdAt: now(),
      label: 'Health Assessment Order'
    },
    products[1] && {
      id: 'purchase2-' + uuidv4().substring(0, 8),
      clientId: clients[0].uid,
      patientId: patients[2].uid,
      productId: products[1].id,
      templateId: products[1].id,
      questionnaireQuantity: 2,
      unitPrice: products[1].price,
      totalPrice: products[1].price * 2,
      isPaid: false,
      date: timestamp(new Date(Date.now() - 2 * 24 * 60 * 60 * 1000)),
      createdAt: now(),
      label: 'Mental Health Survey Order'
    },
    products[2] && {
      id: 'purchase3-' + uuidv4().substring(0, 8),
      clientId: clients[1].uid,
      patientId: patients[1].uid,
      productId: products[2].id,
      templateId: products[2].id,
      questionnaireQuantity: 1,
      unitPrice: products[2].price,
      totalPrice: products[2].price,
      isPaid: true,
      date: timestamp(new Date(Date.now() - 1 * 24 * 60 * 60 * 1000)),
      createdAt: now(),
      label: 'Pediatric Intake Order'
    }
  ].filter(Boolean);

  return {
    [COLLECTIONS.USERS]: users,
    [COLLECTIONS.CLINICS]: clinics,
    [COLLECTIONS.DOCTORS]: doctors,
    [COLLECTIONS.QUESTIONNAIRE_TEMPLATES]: questionnaireTemplates,
    [COLLECTIONS.QUESTIONNAIRES]: questionnaires,
    [COLLECTIONS.PRODUCTS]: products,
    [COLLECTIONS.PURCHASES]: purchases
  } as Record<string, any[]>;
}

// Debug: Log document count for a collection
async function logCollectionCount(collectionName: string) {
  const snapshot = await db.collection(collectionName).get();
  console.log(chalk.gray(`  [VERIFY] ${collectionName}: `) + chalk.bold(snapshot.size) + chalk.gray(' documents'));
}

// Populate a collection with test data
async function populateCollection(collectionName: string, data: any[]): Promise<void> {
  console.log(chalk.bold.blueBright(`Populating collection: `) + chalk.bold(collectionName) + chalk.gray(` with `) + chalk.bold(data.length) + chalk.gray(' documents'));
  const collectionRef = db.collection(collectionName);
  const batchSize = 500;
  let batchCount = 0;
  let docCount = 0;
  let batch = db.batch();

  for (const item of data) {
    let docId = item.id;
    if (collectionName === COLLECTIONS.USERS) {
      docId = item.uid;
    }
    const { id, ...itemData } = item;
    if (!docId || typeof docId !== 'string' || docId.trim() === '') {
      console.error(chalk.redBright(`❌ Skipping item in ${collectionName}: missing or invalid document ID. Item:`), item);
      continue;
    }
    const docRef = collectionRef.doc(docId);
    batch.set(docRef, itemData);
    batchCount++;
    docCount++;
    if (batchCount >= batchSize) {
      await batch.commit();
      console.log(chalk.greenBright(`  Added batch of ${batchCount} documents`));
      batch = db.batch();
      batchCount = 0;
    }
  }
  if (batchCount > 0) {
    await batch.commit();
    console.log(chalk.greenBright(`  Added batch of ${batchCount} documents`));
  }
  console.log(chalk.greenBright(`✅ Added ${docCount} documents to ${collectionName}`));
}

// Populate all specified collections, with debug sections
async function populateCollections(collections: string[], testData: Record<string, any[]>): Promise<void> {
  console.log(chalk.bold.bgBlueBright('\n=== POPULATING COLLECTIONS ==='));

  // Test Clinics
  if (testData[COLLECTIONS.CLINICS]) {
    console.log(chalk.bold.cyan('\n--- Populating Test Clinics ---'));
    await populateCollection(COLLECTIONS.CLINICS, testData[COLLECTIONS.CLINICS]);
    await logCollectionCount(COLLECTIONS.CLINICS);
  }

  // Test Users
  if (testData[COLLECTIONS.USERS]) {
    console.log(chalk.bold.cyan('\n--- Populating Test Users ---'));
    await populateCollection(COLLECTIONS.USERS, testData[COLLECTIONS.USERS]);
    await logCollectionCount(COLLECTIONS.USERS);
  }

  // Test Doctors
  if (testData[COLLECTIONS.DOCTORS]) {
    console.log(chalk.bold.cyan('\n--- Populating Test Doctors ---'));
    await populateCollection(COLLECTIONS.DOCTORS, testData[COLLECTIONS.DOCTORS]);
    await logCollectionCount(COLLECTIONS.DOCTORS);
  }

  // Test Questionnaire Templates
  if (testData[COLLECTIONS.QUESTIONNAIRE_TEMPLATES]) {
    console.log(chalk.bold.cyan('\n--- Populating Test Questionnaire Templates ---'));
    await populateCollection(COLLECTIONS.QUESTIONNAIRE_TEMPLATES, testData[COLLECTIONS.QUESTIONNAIRE_TEMPLATES]);
    await logCollectionCount(COLLECTIONS.QUESTIONNAIRE_TEMPLATES);
  }

  // Test Products
  if (testData[COLLECTIONS.PRODUCTS]) {
    console.log(chalk.bold.cyan('\n--- Populating Test Products ---'));
    await populateCollection(COLLECTIONS.PRODUCTS, testData[COLLECTIONS.PRODUCTS]);
    await logCollectionCount(COLLECTIONS.PRODUCTS);
  }

  // Test Questionnaires
  if (testData[COLLECTIONS.QUESTIONNAIRES]) {
    console.log(chalk.bold.cyan('\n--- Populating Test Questionnaires ---'));
    await populateCollection(COLLECTIONS.QUESTIONNAIRES, testData[COLLECTIONS.QUESTIONNAIRES]);
    await logCollectionCount(COLLECTIONS.QUESTIONNAIRES);
  }

  // Test Purchases
  if (testData[COLLECTIONS.PURCHASES]) {
    console.log(chalk.bold.cyan('\n--- Populating Test Purchases ---'));
    await populateCollection(COLLECTIONS.PURCHASES, testData[COLLECTIONS.PURCHASES]);
    await logCollectionCount(COLLECTIONS.PURCHASES);
  }

  // Add more debug sections for other collections as needed...
  console.log(chalk.greenBright.bold('\n✅ All specified collections populated and verified.'));
}

// Main function to run the script
async function run(): Promise<void> {
  console.log(chalk.bold.bgCyan.black('\nDatabase Population Script (Debug Mode)'));
  console.log(chalk.cyanBright('='.repeat(40)));
  console.log(chalk.cyanBright('Collections to process: ') + chalk.bold(collections.join(', ')));

  try {
    // Clear collections if not skipped
    if (!noClear) {
      console.log(chalk.bold.yellow('\n=== CLEARING COLLECTIONS ==='));
      const reversedCollections = [...collections].reverse();
      for (const collection of reversedCollections) {
        await clearCollection(collection);
        await logCollectionCount(collection);
      }
      console.log(chalk.greenBright('✅ All specified collections cleared'));
    }

    // Populate collections if not clear-only
    if (!clearOnly) {
      const testData = generateTestData();
      await populateCollections(collections, testData);
    }

    console.log(chalk.greenBright.bold('\n✅ Database population completed successfully'));
    process.exit(0);
  } catch (error) {
    console.error(chalk.redBright('❌ Error during database population:'), error);
    process.exit(1);
  }
}

// Clear all documents in a collection
async function clearCollection(collectionName: string): Promise<void> {
  console.log(chalk.gray('Clearing collection: ') + chalk.bold(collectionName));
  const collectionRef = db.collection(collectionName);
  const batchSize = 500;
  let docsDeleted = 0;
  try {
    const snapshot = await collectionRef.limit(batchSize).get();
    if (snapshot.empty) {
      console.log(chalk.gray(`  Collection ${collectionName} is already empty`));
      return;
    }
    let batch = db.batch();
    let batchCount = 0;
    for (const doc of snapshot.docs) {
      batch.delete(doc.ref);
      batchCount++;
      docsDeleted++;
      if (batchCount >= batchSize) {
        await batch.commit();
        console.log(chalk.yellowBright(`  Deleted batch of ${batchCount} documents`));
        batch = db.batch();
        batchCount = 0;
      }
    }
    if (batchCount > 0) {
      await batch.commit();
      console.log(chalk.yellowBright(`  Deleted batch of ${batchCount} documents`));
    }
    if (docsDeleted > 0) {
      await clearCollection(collectionName);
    }
  } catch (error) {
    console.error(chalk.redBright(`❌ Error clearing collection ${collectionName}:`), error);
    throw error;
  }
}

// Run the script
run().catch((error) => {
  console.error(chalk.redBright('Unhandled error:'), error);
  process.exit(1);
});
