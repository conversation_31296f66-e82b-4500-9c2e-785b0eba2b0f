import { Page, Locator, expect } from '@playwright/test';
import { BasePage } from '../../utils/basePage';

/**
 * Products page object representing the products listing page
 */
export class ProductsPage extends BasePage {
  readonly productCards: Locator;
  readonly productTitles: Locator;
  readonly productPrices: Locator;
  readonly productImages: Locator;
  readonly filterDropdown: Locator;
  readonly sortDropdown: Locator;
  readonly searchInput: Locator;
  readonly searchButton: Locator;
  readonly categoryFilters: Locator;

  /**
   * Initialize the Products page object
   * @param page Playwright page
   */
  constructor(page: Page) {
    super(page);
    this.productCards = page.locator('[data-testid="product-card"]');
    this.productTitles = page.locator('[data-testid="product-title"]');
    this.productPrices = page.locator('[data-testid="product-price"]');
    this.productImages = page.locator('[data-testid="product-image"]');
    this.filterDropdown = page.locator('[data-testid="filter-dropdown"]');
    this.sortDropdown = page.locator('[data-testid="sort-dropdown"]');
    this.searchInput = page.locator('[data-testid="search-input"]');
    this.searchButton = page.locator('[data-testid="search-button"]');
    this.categoryFilters = page.locator('[data-testid="category-filter"]');
  }

  /**
   * Navigate to the products page
   */
  async goto() {
    await this.page.goto('/trq/products');
    await this.waitForPageLoad();
  }

  /**
   * Wait for the products page to load
   */
  async waitForPageLoad() {
    await this.page.waitForSelector('[data-testid="product-card"]', { state: 'visible' });
  }

  /**
   * Get the number of products displayed on the page
   * @returns The number of products
   */
  async getProductCount(): Promise<number> {
    return await this.productCards.count();
  }

  /**
   * Click on a product by its index
   * @param index The index of the product to click (0-based)
   */
  async clickProduct(index: number) {
    await this.productCards.nth(index).click();
  }

  /**
   * Click on a product by its title
   * @param title The title of the product to click
   */
  async clickProductByTitle(title: string) {
    await this.page.locator(`[data-testid="product-title"]:has-text("${title}")`).first().click();
  }

  /**
   * Search for products
   * @param searchTerm The search term
   */
  async searchProducts(searchTerm: string) {
    await this.searchInput.fill(searchTerm);
    await this.searchButton.click();
    await this.page.waitForLoadState('networkidle');
  }

  /**
   * Filter products by category
   * @param category The category to filter by
   */
  async filterByCategory(category: string) {
    await this.page.locator(`[data-testid="category-filter"]:has-text("${category}")`).click();
    await this.page.waitForLoadState('networkidle');
  }

  /**
   * Sort products
   * @param sortOption The sort option (e.g., 'Price: Low to High')
   */
  async sortProducts(sortOption: string) {
    await this.sortDropdown.click();
    await this.page.locator(`[data-testid="sort-option"]:has-text("${sortOption}")`).click();
    await this.page.waitForLoadState('networkidle');
  }

  /**
   * Get all product titles
   * @returns Array of product titles
   */
  async getProductTitles(): Promise<string[]> {
    const count = await this.productTitles.count();
    const titles: string[] = [];

    for (let i = 0; i < count; i++) {
      titles.push((await this.productTitles.nth(i).textContent()) || '');
    }

    return titles;
  }

  /**
   * Get all product prices
   * @returns Array of product prices
   */
  async getProductPrices(): Promise<string[]> {
    const count = await this.productPrices.count();
    const prices: string[] = [];

    for (let i = 0; i < count; i++) {
      prices.push((await this.productPrices.nth(i).textContent()) || '');
    }

    return prices;
  }

  /**
   * Verify that products are displayed
   */
  async verifyProductsDisplayed() {
    const count = await this.getProductCount();
    expect(count).toBeGreaterThan(0);
  }
}
