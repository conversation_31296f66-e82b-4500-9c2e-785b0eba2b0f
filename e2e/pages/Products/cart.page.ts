import { Page, Locator, expect } from '@playwright/test';
import { BasePage } from '../../utils/basePage';

/**
 * Cart page object representing the shopping cart page
 */
export class CartPage extends BasePage {
  readonly cartItems: Locator;
  readonly cartItemTitles: Locator;
  readonly cartItemPrices: Locator;
  readonly cartItemQuantities: Locator;
  readonly cartItemRemoveButtons: Locator;
  readonly cartItemUpdateButtons: Locator;
  readonly cartSubtotal: Locator;
  readonly cartTax: Locator;
  readonly cartTotal: Locator;
  readonly checkoutButton: Locator;
  readonly continueShoppingButton: Locator;
  readonly emptyCartMessage: Locator;
  readonly cartItemQuantityInputs: Locator;

  /**
   * Initialize the Cart page object
   * @param page Playwright page
   */
  constructor(page: Page) {
    super(page);
    this.cartItems = page.locator('[data-testid="cart-item"]');
    this.cartItemTitles = page.locator('[data-testid="cart-item-title"]');
    this.cartItemPrices = page.locator('[data-testid="cart-item-price"]');
    this.cartItemQuantities = page.locator('[data-testid="cart-item-quantity"]');
    this.cartItemRemoveButtons = page.locator('[data-testid="cart-item-remove"]');
    this.cartItemUpdateButtons = page.locator('[data-testid="cart-item-update"]');
    this.cartSubtotal = page.locator('[data-testid="cart-subtotal"]');
    this.cartTax = page.locator('[data-testid="cart-tax"]');
    this.cartTotal = page.locator('[data-testid="cart-total"]');
    this.checkoutButton = page.locator('[data-testid="checkout-button"]');
    this.continueShoppingButton = page.locator('[data-testid="continue-shopping-button"]');
    this.emptyCartMessage = page.locator('[data-testid="empty-cart-message"]');
    this.cartItemQuantityInputs = page.locator('[data-testid="cart-item-quantity-input"]');
  }

  /**
   * Navigate to the cart page
   */
  async goto() {
    await this.page.goto('/trq/purchases/cart');
    await this.waitForPageLoad();
  }

  /**
   * Wait for the cart page to load
   */
  async waitForPageLoad() {
    // Wait for either cart items or empty cart message
    await Promise.race([
      this.page.waitForSelector('[data-testid="cart-item"]', { state: 'visible', timeout: 5000 }).catch(() => {}),
      this.page.waitForSelector('[data-testid="empty-cart-message"]', { state: 'visible', timeout: 5000 }).catch(() => {})
    ]);

    // Additional wait for page stability
    await this.page.waitForLoadState('networkidle');
  }

  /**
   * Get the number of items in the cart
   * @returns The number of items
   */
  async getItemCount(): Promise<number> {
    return await this.cartItems.count();
  }

  /**
   * Check if the cart is empty
   * @returns True if the cart is empty, false otherwise
   */
  async isCartEmpty(): Promise<boolean> {
    return await this.emptyCartMessage.isVisible();
  }

  /**
   * Get all cart item titles
   * @returns Array of cart item titles
   */
  async getItemTitles(): Promise<string[]> {
    const count = await this.cartItemTitles.count();
    const titles: string[] = [];

    for (let i = 0; i < count; i++) {
      titles.push((await this.cartItemTitles.nth(i).textContent()) || '');
    }

    return titles;
  }

  /**
   * Get the quantity of a specific item by index
   * @param index The index of the item (0-based)
   * @returns The quantity of the item
   */
  async getItemQuantity(index: number): Promise<number> {
    const quantityText = await this.cartItemQuantities.nth(index).textContent();
    return parseInt(quantityText || '0', 10);
  }

  /**
   * Update the quantity of a specific item
   * @param index The index of the item (0-based)
   * @param quantity The new quantity
   */
  async updateItemQuantity(index: number, quantity: number) {
    await this.cartItemQuantityInputs.nth(index).fill(quantity.toString());
    await this.cartItemUpdateButtons.nth(index).click();
    await this.page.waitForLoadState('networkidle');
  }

  /**
   * Remove an item from the cart
   * @param index The index of the item to remove (0-based)
   */
  async removeItem(index: number) {
    await this.cartItemRemoveButtons.nth(index).click();
    await this.page.waitForLoadState('networkidle');
  }

  /**
   * Get the subtotal amount
   * @returns The subtotal amount as a string
   */
  async getSubtotal(): Promise<string> {
    return (await this.cartSubtotal.textContent()) || '';
  }

  /**
   * Get the tax amount
   * @returns The tax amount as a string
   */
  async getTax(): Promise<string> {
    return (await this.cartTax.textContent()) || '';
  }

  /**
   * Get the total amount
   * @returns The total amount as a string
   */
  async getTotal(): Promise<string> {
    return (await this.cartTotal.textContent()) || '';
  }

  /**
   * Proceed to checkout
   */
  async proceedToCheckout() {
    await this.checkoutButton.click();
    // Wait for navigation to checkout page
    await this.page.waitForURL('**/purchases/checkout');
  }

  /**
   * Continue shopping (navigate back to products)
   */
  async continueShopping() {
    await this.continueShoppingButton.click();
    // Wait for navigation to products page
    await this.page.waitForURL('**/products');
  }

  /**
   * Verify that a specific product is in the cart
   * @param productTitle The title of the product
   * @param expectedQuantity The expected quantity
   */
  async verifyProductInCart(productTitle: string, expectedQuantity: number) {
    const titles = await this.getItemTitles();
    const index = titles.findIndex((title) => title.includes(productTitle));

    expect(index).toBeGreaterThanOrEqual(0);

    if (index >= 0) {
      const quantity = await this.getItemQuantity(index);
      expect(quantity).toBe(expectedQuantity);
    }
  }
}
