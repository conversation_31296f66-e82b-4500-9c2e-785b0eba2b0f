// color variants
@use 'themes-vars.module.scss' as *;

// third party
@use 'react-perfect-scrollbar/dist/css/styles.css';
@use 'slick-carousel/slick/slick.css';
@use 'slick-carousel/slick/slick-theme.css';

@use 'react-18-image-lightbox/style.css';
@use 'react-responsive-carousel/lib/styles/carousel.min.css';
@use './scrollbar.scss';

// ==============================|| TAP HIGHLIGHT ||============================== //

* {
  -webkit-tap-highlight-color: transparent !important;
}

// ==============================|| LIGHT BOX ||============================== //
.slick-arrow:before {
  color: $grey500;
}

// ==============================|| LIGHT BOX ||============================== //
.fullscreen .react-images__blanket {
  z-index: 1200;
}

// ==============================|| APEXCHART ||============================== //

.apexcharts-legend-series .apexcharts-legend-marker {
  left: -4px !important;
  right: -4px !important;
}

.apexcharts-legend.apx-legend-position-bottom .apexcharts-legend-series,
.apexcharts-legend.apx-legend-position-top .apexcharts-legend-series {
  gap: 8px;
}

.apexcharts-canvas {
  .apexcharts-tooltip-series-group.apexcharts-active,
  .apexcharts-tooltip-series-group:last-child {
    padding-bottom: 0;
  }
}

.apexcharts-legend-series {
  align-items: center;
  display: flex;
  gap: 8px;
}

// ==============================|| PERFECT SCROLLBAR ||============================== //

.scrollbar-container {
  .ps__rail-y {
    &:hover > .ps__thumb-y,
    &:focus > .ps__thumb-y,
    &.ps--clicking .ps__thumb-y {
      background-color: $grey500;
      width: 5px;
    }
  }
  .ps__thumb-y {
    background-color: $grey500;
    border-radius: 6px;
    width: 5px;
    right: 0;
  }
}

.scrollbar-container.ps,
.scrollbar-container > .ps {
  &.ps--active-y > .ps__rail-y {
    width: 5px;
    background-color: transparent !important;
    z-index: 999;
    &:hover,
    &.ps--clicking {
      width: 5px;
      background-color: transparent;
    }
  }
  &.ps--scrolling-y > .ps__rail-y,
  &.ps--scrolling-x > .ps__rail-x {
    opacity: 0.4;
    background-color: transparent;
  }
}

.blog-slider .slick-slide {
  padding: 0 5px;
}

.blog-slider .slick-track {
  padding: 0 -5px;
}

// ==============================|| ANIMATION KEYFRAMES ||============================== //

@keyframes wings {
  50% {
    transform: translateY(-40px);
  }
  100% {
    transform: translateY(0px);
  }
}

@keyframes wingsReverse {
  50% {
    transform: translateY(40px) scaleX(-1);
  }
  100% {
    transform: translateY(0px) scaleX(-1);
  }
}

@keyframes blink {
  50% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}

@keyframes bounce {
  0%,
  20%,
  53%,
  to {
    animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
    transform: translateZ(0);
  }
  40%,
  43% {
    animation-timing-function: cubic-bezier(0.755, 0.05, 0.855, 0.06);
    transform: translate3d(0, -5px, 0);
  }
  70% {
    animation-timing-function: cubic-bezier(0.755, 0.05, 0.855, 0.06);
    transform: translate3d(0, -7px, 0);
  }
  80% {
    transition-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
    transform: translateZ(0);
  }
  90% {
    transform: translate3d(0, -2px, 0);
  }
}

@keyframes slideY {
  0%,
  50%,
  100% {
    transform: translateY(0px);
  }
  25% {
    transform: translateY(-10px);
  }
  75% {
    transform: translateY(10px);
  }
}

@keyframes slideX {
  0%,
  50%,
  100% {
    transform: translateX(0px);
  }
  25% {
    transform: translateX(-10px);
  }
  75% {
    transform: translateX(10px);
  }
}

// ==============================|| ANIMATION KEYFRAMES ||============================== //

.preBuildDashBoard-slider {
  overflow: hidden;
  .slider {
    .slide {
      opacity: 0.5;
      display: flex;
      justify-content: center;
      .custom-slider {
        gap: 40px;
      }
      &.selected {
        opacity: 1;
        .custom-slider {
          display: flex;
          flex-direction: column;
        }
      }
      &:not(.selected) {
        transform: scale(0.7);
        transform-origin: top;
      }
    }
  }
}

.project-info {
  .project-slider {
    .slick-list {
      padding: 0 !important;
    }
  }
  .slick-slide {
    opacity: 0.05;
  }
  .slick-active {
    opacity: 0.2;
  }
  .slick-current {
    opacity: 1 !important;
    .MuiTypography-root {
      color: $primaryMain;
    }
  }
}

.ril__inner {
  direction: ltr;
}
.ReactModal__Overlay {
  z-index: 99999 !important;
}

.MuiTreeItem-content {
  padding-top: 0px;
  padding-bottom: 0px;
}

.MuiDataGrid-menu {
  .MuiPaper-root {
    box-shadow:
      0 2px 4px rgba(0, 0, 0, 0.2),
      0 1px 3px rgba(0, 0, 0, 0.14) !important;
  }
}

.notistack-Snackbar {
  .notistack-MuiContent-warning {
    background-color: $warningMain;
    color: $grey900;
  }
  .notistack-MuiContent-success {
    background-color: $successMain;
    color: $grey900;
  }
  .notistack-MuiContent-error {
    background-color: $errorMain;
  }
  .notistack-MuiContent-info {
    background-color: #0288d1;
  }
}

html[dir='ltr'] .custom-data-grid.MuiDataGrid-root {
  .MuiDataGrid-columnHeader,
  .MuiDataGrid-cell {
    &:focus,
    &:focus-within {
      outline: none;
      border: solid $primaryMain 1px;
    }
  }
  .MuiDataGrid-row:last-child {
    .MuiDataGrid-cell[aria-colindex='1'] {
      &:focus,
      &:focus-within {
        border-bottom-left-radius: 8px;
      }
    }
    .MuiDataGrid-cell:last-child {
      border-bottom-right-radius: 8px;
    }
  }
}

html[dir='rtl'] {
  .scrollbar-container {
    .ps__thumb-y {
      right: 10px !important;
    }
  }
  .notistack-MuiContent-default {
    gap: 16px;
  }
  .notistack-MuiContent {
    > div:last-child {
      padding-left: unset;
      padding-right: 16px;
    }
  }

  .apexcharts-tooltip .apexcharts-tooltip-marker {
    margin-left: 10px;
    margin-right: 0px;
  }

  .apexcharts-tooltip-text-y-value {
    margin-left: 0px;
    margin-right: 5px;
  }

  .custom-data-grid.MuiDataGrid-root {
    .MuiDataGrid-columnHeader,
    .MuiDataGrid-cell {
      &:focus,
      &:focus-within {
        outline: none;
        border: solid $primaryMain 1px;
      }
    }

    .MuiDataGrid-row:last-child {
      .MuiDataGrid-cell[data-colindex='0'] {
        &:focus,
        &:focus-within {
          border-bottom-right-radius: 8px;
        }
      }
      .MuiDataGrid-cell:last-child {
        border-bottom-left-radius: 8px;
      }
    }
  }
}
