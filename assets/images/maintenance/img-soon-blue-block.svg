<svg width="713" height="400" viewBox="0 0 713 400" fill="none" xmlns="http://www.w3.org/2000/svg">
<rect width="26.998" height="26.8293" transform="matrix(0.866041 -0.499972 0.866041 0.499972 291.67 86.4912)" fill="#E3F2FD"/>
<rect width="24.2748" height="24.1231" transform="matrix(0.866041 -0.499972 0.866041 0.499972 294.046 87.48)" fill="#90CAF9"/>
<g filter="url(#filter0_d)">
<path d="M424.188 172.48L412.163 165.538V181.571L412.187 181.558C412.311 182.582 413.051 183.587 414.408 184.37L529.315 250.707C532.301 252.431 537.144 252.431 540.131 250.707L646.476 189.313C648.071 188.392 648.814 187.165 648.705 185.959V170.284L636.519 177.32L531.569 116.732C528.582 115.008 523.74 115.008 520.753 116.732L424.188 172.48Z" fill="url(#paint0_linear)"/>
</g>
<rect width="135.283" height="145.169" rx="5" transform="matrix(0.866041 -0.499972 0.866041 0.499972 409 165.638)" fill="#90CAF9"/>
<rect width="135.283" height="145.169" rx="5" transform="matrix(0.866041 -0.499972 0.866041 0.499972 409 165.638)" fill="url(#paint1_linear)"/>
<rect width="128.598" height="137.995" rx="5" transform="matrix(0.866041 -0.499972 0.866041 0.499972 415.002 165.761)" fill="url(#paint2_linear)"/>
<defs>
<filter id="filter0_d" x="348.163" y="115.439" width="364.552" height="284.561" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"/>
<feOffset dy="84"/>
<feGaussianBlur stdDeviation="32"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.129412 0 0 0 0 0.588235 0 0 0 0 0.952941 0 0 0 0.2 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow" result="shape"/>
</filter>
<linearGradient id="paint0_linear" x1="530.44" y1="158.777" x2="412.163" y2="183.719" gradientUnits="userSpaceOnUse">
<stop stop-color="#2196F3"/>
<stop offset="1" stop-color="#B1DCFF"/>
</linearGradient>
<linearGradient id="paint1_linear" x1="1.14127" y1="6.08432" x2="46.0633" y2="95.9332" gradientUnits="userSpaceOnUse">
<stop stop-color="#FAFAFA" stop-opacity="0.74"/>
<stop offset="1" stop-color="#91CBFA"/>
</linearGradient>
<linearGradient id="paint2_linear" x1="-42.0844" y1="-101.822" x2="33.5428" y2="92.0592" gradientUnits="userSpaceOnUse">
<stop stop-color="#FAFAFA" stop-opacity="0.74"/>
<stop offset="1" stop-color="#91CBFA"/>
</linearGradient>
</defs>
</svg>
