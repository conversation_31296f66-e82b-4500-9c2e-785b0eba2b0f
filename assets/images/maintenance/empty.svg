<svg width="751" height="441" viewBox="0 0 751 441" fill="none" xmlns="http://www.w3.org/2000/svg">
<rect width="29.8893" height="29.7025" transform="matrix(0.866041 -0.499972 0.866041 0.499972 141.711 171.32)" fill="#EDE7F6"/>
<rect width="27.6701" height="27.4972" transform="matrix(0.866041 -0.499972 0.866041 0.499972 143.621 172.422)" fill="#B39DDB"/>
<rect width="29.8893" height="29.7025" transform="matrix(0.866041 -0.499972 0.866041 0.499972 558.711 201.253)" fill="#EDE7F6"/>
<rect width="27.6701" height="27.4972" transform="matrix(0.866041 -0.499972 0.866041 0.499972 560.621 202.355)" fill="#B39DDB"/>
<rect width="29.9693" height="29.782" transform="matrix(0.866041 -0.499972 0.866041 0.499972 351 351.984)" fill="#E3F2FD"/>
<rect width="26.9465" height="26.7781" transform="matrix(0.866041 -0.499972 0.866041 0.499972 353.637 353.082)" fill="#90CAF9"/>
<g opacity="0.09">
<line y1="-0.5" x2="433.021" y2="-0.5" transform="matrix(0.866041 -0.499972 -0.866041 -0.499972 5.62109 219.271)" stroke="black"/>
<line y1="-0.5" x2="433.021" y2="-0.5" transform="matrix(-0.866041 -0.499972 -0.866041 0.499972 380.625 430.234)" stroke="black"/>
<line y1="-0.5" x2="433.021" y2="-0.5" transform="matrix(0.866041 -0.499972 -0.866041 -0.499972 31.7234 234.339)" stroke="black"/>
<line y1="-0.5" x2="433.021" y2="-0.5" transform="matrix(-0.866041 -0.499972 -0.866041 0.499972 406.727 415.165)" stroke="black"/>
<line y1="-0.5" x2="433.021" y2="-0.5" transform="matrix(0.866041 -0.499972 -0.866041 -0.499972 57.8259 249.408)" stroke="black"/>
<line y1="-0.5" x2="433.021" y2="-0.5" transform="matrix(-0.866041 -0.499972 -0.866041 0.499972 432.83 400.096)" stroke="black"/>
<line y1="-0.5" x2="433.021" y2="-0.5" transform="matrix(0.866041 -0.499972 -0.866041 -0.499972 83.9282 264.478)" stroke="black"/>
<line y1="-0.5" x2="433.021" y2="-0.5" transform="matrix(-0.866041 -0.499972 -0.866041 0.499972 458.932 385.026)" stroke="black"/>
<line y1="-0.5" x2="433.021" y2="-0.5" transform="matrix(0.866041 -0.499972 -0.866041 -0.499972 110.03 279.546)" stroke="black"/>
<line y1="-0.5" x2="433.021" y2="-0.5" transform="matrix(-0.866041 -0.499972 -0.866041 0.499972 485.034 369.957)" stroke="black"/>
<line y1="-0.5" x2="433.021" y2="-0.5" transform="matrix(0.866041 -0.499972 -0.866041 -0.499972 136.133 294.616)" stroke="black"/>
<line y1="-0.5" x2="433.021" y2="-0.5" transform="matrix(-0.866041 -0.499972 -0.866041 0.499972 511.137 354.889)" stroke="black"/>
<line y1="-0.5" x2="433.021" y2="-0.5" transform="matrix(0.866041 -0.499972 -0.866041 -0.499972 162.235 309.684)" stroke="black"/>
<line y1="-0.5" x2="433.021" y2="-0.5" transform="matrix(-0.866041 -0.499972 -0.866041 0.499972 537.239 339.819)" stroke="black"/>
<line y1="-0.5" x2="433.021" y2="-0.5" transform="matrix(0.866041 -0.499972 -0.866041 -0.499972 188.337 324.754)" stroke="black"/>
<line y1="-0.5" x2="433.021" y2="-0.5" transform="matrix(-0.866041 -0.499972 -0.866041 0.499972 563.341 324.749)" stroke="black"/>
<line y1="-0.5" x2="433.021" y2="-0.5" transform="matrix(0.866041 -0.499972 -0.866041 -0.499972 214.429 339.819)" stroke="black"/>
<line y1="-0.5" x2="433.021" y2="-0.5" transform="matrix(-0.866041 -0.499972 -0.866041 0.499972 589.444 309.681)" stroke="black"/>
<line y1="-0.5" x2="433.021" y2="-0.5" transform="matrix(0.866041 -0.499972 -0.866041 -0.499972 240.531 354.889)" stroke="black"/>
<line y1="-0.5" x2="433.021" y2="-0.5" transform="matrix(-0.866041 -0.499972 -0.866041 0.499972 615.546 294.612)" stroke="black"/>
<line y1="-0.5" x2="433.021" y2="-0.5" transform="matrix(0.866041 -0.499972 -0.866041 -0.499972 266.634 369.957)" stroke="black"/>
<line y1="-0.5" x2="433.021" y2="-0.5" transform="matrix(-0.866041 -0.499972 -0.866041 0.499972 641.648 279.543)" stroke="black"/>
<path d="M292.837 385.084L667.852 168.586" stroke="black"/>
<line y1="-0.5" x2="433.021" y2="-0.5" transform="matrix(-0.866041 -0.499972 -0.866041 0.499972 667.75 264.474)" stroke="black"/>
<line y1="-0.5" x2="433.021" y2="-0.5" transform="matrix(0.866041 -0.499972 -0.866041 -0.499972 318.839 400.096)" stroke="black"/>
<line y1="-0.5" x2="433.021" y2="-0.5" transform="matrix(-0.866041 -0.499972 -0.866041 0.499972 693.853 249.405)" stroke="black"/>
<line y1="-0.5" x2="433.021" y2="-0.5" transform="matrix(0.866041 -0.499972 -0.866041 -0.499972 344.941 415.165)" stroke="black"/>
<line y1="-0.5" x2="433.021" y2="-0.5" transform="matrix(-0.866041 -0.499972 -0.866041 0.499972 719.955 234.335)" stroke="black"/>
<line y1="-0.5" x2="433.021" y2="-0.5" transform="matrix(0.866041 -0.499972 -0.866041 -0.499972 371.043 430.234)" stroke="black"/>
<line y1="-0.5" x2="433.021" y2="-0.5" transform="matrix(-0.866041 -0.499972 -0.866041 0.499972 746.057 219.267)" stroke="black"/>
</g>
<g filter="url(#filter0_d)">
<g filter="url(#filter1_d)">
<path d="M382.446 100.074L280.771 158.076L280.771 181.518V238.65C280.646 241.345 282.37 244.067 285.925 246.119L360.466 289.22C367.304 293.168 378.495 293.168 385.333 289.22L484.8 231.792C488.466 229.676 490.167 226.837 489.902 224.051L489.93 186.276V142.076L474.292 135.218L417.413 100.074C410.575 96.1265 389.284 96.1261 382.446 100.074Z" fill="#90CAF9"/>
</g>
<g filter="url(#filter2_d)">
<path d="M256 197.218L280.858 155.788C279.944 160.131 283.715 163.312 285.715 164.36C307.716 177.122 352.86 203.332 357.431 206.075C362.003 208.818 368.003 210.075 370.289 210.647L350.574 251.791L256 197.218Z" fill="url(#paint0_linear)"/>
</g>
<path d="M406.004 104.072V91.5005L487.435 137.502L516.293 174.074L406.004 104.072Z" fill="url(#paint1_linear)"/>
<path d="M385.329 92.6785L285.862 150.106C279.024 154.054 279.086 160.478 285.925 164.426L360.466 207.528C367.304 211.475 378.495 211.475 385.333 207.527L484.8 150.099C491.638 146.151 491.638 139.691 484.8 135.743L410.197 92.6777C403.358 88.73 392.168 88.7303 385.329 92.6785Z" fill="url(#paint2_linear)"/>
<path d="M360.417 207.825L331.798 191.105L397.247 152.967L438.255 177.053L384.704 208.111C377.865 212.059 367.256 211.773 360.417 207.825Z" fill="url(#paint3_linear)"/>
<g filter="url(#filter3_d)">
<path d="M398.29 248.648L376.861 210.647C381.432 210.075 383.908 208.456 386.575 206.932L483.149 150.931C490.292 147.502 489.721 141.502 489.721 141.502L509.722 180.932L398.29 248.648Z" fill="url(#paint4_linear)"/>
</g>
<path d="M382.441 121.055L372.698 126.68L371.318 80.1797L383.821 72.9609L382.441 121.055ZM377.57 131.273C379.545 130.133 381.128 129.891 382.319 130.547C383.537 131.188 384.146 132.555 384.146 134.648C384.146 136.711 383.537 138.766 382.319 140.812C381.128 142.844 379.545 144.43 377.57 145.57C375.621 146.695 374.038 146.938 372.82 146.297C371.629 145.641 371.034 144.281 371.034 142.219C371.034 140.156 371.629 138.109 372.82 136.078C374.038 134 375.621 132.398 377.57 131.273Z" fill="#673AB7"/>
<path d="M373 146.5C367.5 142.5 373.5 130.5 381 130H387C386 132.833 383.7 139.1 382.5 141.5C381.93 142.64 386 146.5 378.5 147C378.5 147 375.5 147.5 373 146.5Z" fill="url(#paint5_linear)"/>
<path d="M388.441 121.055L378.698 126.68L377.318 80.1797L389.821 72.9609L388.441 121.055ZM383.57 131.273C385.545 130.133 387.128 129.891 388.319 130.547C389.537 131.188 390.146 132.555 390.146 134.648C390.146 136.711 389.537 138.766 388.319 140.812C387.128 142.844 385.545 144.43 383.57 145.57C381.621 146.695 380.038 146.938 378.82 146.297C377.629 145.641 377.034 144.281 377.034 142.219C377.034 140.156 377.629 138.109 378.82 136.078C380.038 134 381.621 132.398 383.57 131.273Z" fill="#B39DDB"/>
<path d="M383 73H390L377.5 80.5L371.5 80L383 73Z" fill="url(#paint6_linear)"/>
<path d="M372.5 126.5L371 80L377.5 80.5L379 126.5H372.5Z" fill="url(#paint7_linear)"/>
</g>
<defs>
<filter id="filter0_d" x="194" y="72.9609" width="384.293" height="365.22" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"/>
<feOffset dy="84"/>
<feGaussianBlur stdDeviation="31"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.129412 0 0 0 0 0.588235 0 0 0 0 0.952941 0 0 0 0.1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow" result="shape"/>
</filter>
<filter id="filter1_d" x="216.765" y="97.1133" width="337.165" height="343.068" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"/>
<feOffset dy="84"/>
<feGaussianBlur stdDeviation="32"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.890196 0 0 0 0 0.94902 0 0 0 0 0.992157 0 0 0 0.5 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow" result="shape"/>
</filter>
<filter id="filter2_d" x="246" y="145.788" width="138.289" height="120.003" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"/>
<feOffset dx="2" dy="2"/>
<feGaussianBlur stdDeviation="6"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.04 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow" result="shape"/>
</filter>
<filter id="filter3_d" x="361.861" y="138.502" width="156.861" height="131.146" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"/>
<feOffset dx="-3" dy="9"/>
<feGaussianBlur stdDeviation="6"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.04 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow" result="shape"/>
</filter>
<linearGradient id="paint0_linear" x1="361.605" y1="269.884" x2="276.866" y2="174.073" gradientUnits="userSpaceOnUse">
<stop stop-color="#E3F2FD"/>
<stop offset="1" stop-color="#90CAF9"/>
</linearGradient>
<linearGradient id="paint1_linear" x1="471.435" y1="132.073" x2="505.15" y2="160.645" gradientUnits="userSpaceOnUse">
<stop stop-color="#1565C0"/>
<stop offset="1" stop-color="#1E88E5"/>
</linearGradient>
<linearGradient id="paint2_linear" x1="401.633" y1="38.6589" x2="328.908" y2="248.755" gradientUnits="userSpaceOnUse">
<stop stop-color="#E3F2FD"/>
<stop offset="1" stop-color="#2196F3"/>
</linearGradient>
<linearGradient id="paint3_linear" x1="348.405" y1="115.734" x2="353.931" y2="237.309" gradientUnits="userSpaceOnUse">
<stop stop-color="#90CAF9"/>
<stop offset="1" stop-color="#1E88E5"/>
</linearGradient>
<linearGradient id="paint4_linear" x1="404.576" y1="256.934" x2="426.576" y2="149.788" gradientUnits="userSpaceOnUse">
<stop stop-color="#E3F2FD"/>
<stop offset="1" stop-color="#90CAF9"/>
</linearGradient>
<linearGradient id="paint5_linear" x1="371" y1="131.5" x2="380.5" y2="146.5" gradientUnits="userSpaceOnUse">
<stop stop-color="#EDE7F6"/>
<stop offset="1" stop-color="#673AB7"/>
</linearGradient>
<linearGradient id="paint6_linear" x1="390" y1="72.5" x2="367" y2="82" gradientUnits="userSpaceOnUse">
<stop stop-color="#673AB7"/>
<stop offset="0.0001" stop-color="#673AB7"/>
<stop offset="1" stop-color="#EDE7F6"/>
</linearGradient>
<linearGradient id="paint7_linear" x1="375" y1="125.5" x2="373.5" y2="64.5" gradientUnits="userSpaceOnUse">
<stop stop-color="#EDE7F6"/>
<stop offset="1" stop-color="#B39DDB"/>
</linearGradient>
</defs>
</svg>
