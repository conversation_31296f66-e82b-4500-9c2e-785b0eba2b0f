import { useEffect, useState, useMemo } from 'react';
import { getProducts, Product } from '../[services]/productsService';
import ProductCard from '../[components]/ProductCard';
import {
  Box,
  Typography,
  Grid,
  TextField,
  Container,
  Divider,
  Card,
  CardContent,
  InputAdornment,
  FormControl,
  Select,
  MenuItem,
  Chip,
  Stack,
  Pagination,
  ToggleButton,
  ToggleButtonGroup,
  Skeleton,
  Paper,
  Button,
  Tooltip,
  IconButton,
  Menu,
  Popover,
  Slider,
  Badge,
  FormControlLabel,
  Checkbox,
  useTheme,
  useMediaQuery,
  SelectChangeEvent
} from '@mui/material';

// Icons
import SearchIcon from '@mui/icons-material/Search';
import GridViewIcon from '@mui/icons-material/GridView';
import ViewListIcon from '@mui/icons-material/ViewList';
import RefreshIcon from '@mui/icons-material/Refresh';
import TuneIcon from '@mui/icons-material/Tune';
import ClearIcon from '@mui/icons-material/Clear';
import NewReleasesIcon from '@mui/icons-material/NewReleases';
import LocalOfferIcon from '@mui/icons-material/LocalOffer';
import FilterListIcon from '@mui/icons-material/FilterList';
import SortIcon from '@mui/icons-material/Sort';
import {
  Box,
  Typography,
  Grid,
  TextField,
  Container,
  Divider,
  Card,
  CardContent,
  InputAdornment,
  FormControl,
  Select,
  MenuItem,
  Chip,
  Stack,
  Pagination,
  ToggleButton,
  ToggleButtonGroup,
  Skeleton,
  Paper,
  Button,
  Tooltip,
  IconButton,
  Menu,
  Popover,
  Slider,
  Badge,
  FormControlLabel,
  Checkbox,
  useTheme,
  useMediaQuery,
  SelectChangeEvent
} from '@mui/material';

// Icons
import SearchIcon from '@mui/icons-material/Search';
import GridViewIcon from '@mui/icons-material/GridView';
import ViewListIcon from '@mui/icons-material/ViewList';
import RefreshIcon from '@mui/icons-material/Refresh';
import TuneIcon from '@mui/icons-material/Tune';
import ClearIcon from '@mui/icons-material/Clear';
import NewReleasesIcon from '@mui/icons-material/NewReleases';
import LocalOfferIcon from '@mui/icons-material/LocalOffer';
import FilterListIcon from '@mui/icons-material/FilterList';
import SortIcon from '@mui/icons-material/Sort';

export default function Products() {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const isTablet = useMediaQuery(theme.breakpoints.down('md'));

  // State variables
  const [products, setProducts] = useState<Product[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [sortBy, setSortBy] = useState<string>('default');
  const [selectedCategories, setSelectedCategories] = useState<string[]>([]);
  const [priceRange, setPriceRange] = useState<[number, number]>([0, 1000]);
  const [showNewOnly, setShowNewOnly] = useState(false);
  const [showDiscountsOnly, setShowDiscountsOnly] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 8;
  
  // Filter popover state
  const [filterAnchorEl, setFilterAnchorEl] = useState<HTMLButtonElement | null>(null);
  const [sortAnchorEl, setSortAnchorEl] = useState<HTMLButtonElement | null>(null);
  const openFilterPopover = Boolean(filterAnchorEl);
  const openSortMenu = Boolean(sortAnchorEl);

  // Extract unique categories from products for filter options
  const categories = useMemo(() => {
    const categorySet = new Set<string>();
    products.forEach((product) => {
      if (product.category) categorySet.add(product.category);
    });
    return Array.from(categorySet);
  }, [products]);

  // Calculate price range from products
  const priceRangeBounds = useMemo(() => {
    if (products.length === 0) return [0, 1000];
    let min = Number.MAX_VALUE;
    let max = 0;
    products.forEach((product) => {
      if (product.price) {
        min = Math.min(min, product.price);
        max = Math.max(max, product.price);
      }
    });
    return [Math.floor(min), Math.ceil(max)];
  }, [products]);

  // Reset filters
  const resetFilters = () => {
    setSelectedCategories([]);
    setPriceRange(priceRangeBounds);
    setShowNewOnly(false);
    setShowDiscountsOnly(false);
    setSearchTerm('');
    setSortBy('default');
    setCurrentPage(1);
  };

  // Filter and sort products
  const filteredProducts = useMemo(() => {
    return products
      .filter((product) => {
        // Search filter
        const matchesSearch =
          searchTerm === '' ||
          product.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
          product.description?.toLowerCase().includes(searchTerm.toLowerCase());

        // Category filter
        const matchesCategory = selectedCategories.length === 0 || 
          (product.category && selectedCategories.includes(product.category));

        // Price range filter
        const matchesPrice = (product.price || 0) >= priceRange[0] && (product.price || 0) <= priceRange[1];

        // New products filter
        const matchesNew = !showNewOnly || product.isNew === true;

        // Discount products filter
        const matchesDiscount = !showDiscountsOnly || 
          (product.originalPrice && product.originalPrice > product.price);

        return matchesSearch && matchesCategory && matchesPrice && matchesNew && matchesDiscount;
      })
      .sort((a, b) => {
        // Sort results
        switch (sortBy) {
          case 'price-low':
            return (a.price || 0) - (b.price || 0);
          case 'price-high':
            return (b.price || 0) - (a.price || 0);
          case 'name-asc':
            return (a.name || '').localeCompare(b.name || '');
          case 'name-desc':
            return (b.name || '').localeCompare(a.name || '');
          case 'rating-high':
            return (b.rating || 0) - (a.rating || 0);
          case 'newest':
            return new Date(b.createdAt || 0).getTime() - new Date(a.createdAt || 0).getTime();
          default:
            return 0;
        }
      });
  }, [products, searchTerm, selectedCategories, priceRange, showNewOnly, showDiscountsOnly, sortBy]);

  // Pagination
  const paginatedProducts = useMemo(() => {
    const startIndex = (currentPage - 1) * itemsPerPage;
    return filteredProducts.slice(startIndex, startIndex + itemsPerPage);
  }, [filteredProducts, currentPage, itemsPerPage]);

  // Load products
  useEffect(() => {
    const fetchProducts = async () => {
      setLoading(true);
      try {
        const data = await getProducts();
        setProducts(data);
        // Initialize price range based on actual product prices
        if (data.length > 0) {
          const prices = data.map(p => p.price || 0).filter(Boolean);
          const min = Math.floor(Math.min(...prices));
          const max = Math.ceil(Math.max(...prices));
          setPriceRange([min, max]);
        }
      } catch (error) {
        console.error('Error fetching products:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchProducts();
  }, []);

  // Handle filter popover open/close
  const handleFilterClick = (event: React.MouseEvent<HTMLButtonElement>) => {
    setFilterAnchorEl(event.currentTarget);
  };

  const handleFilterClose = () => {
    setFilterAnchorEl(null);
  };

  // Handle sort menu open/close
  const handleSortClick = (event: React.MouseEvent<HTMLButtonElement>) => {
    setSortAnchorEl(event.currentTarget);
  };

  const handleSortClose = () => {
    setSortAnchorEl(null);
  };

  // Handle category selection
  const handleCategoryChange = (event: SelectChangeEvent<string[]>) => {
    const { value } = event.target;
    setSelectedCategories(typeof value === 'string' ? value.split(',') : value);
    setCurrentPage(1); // Reset to first page when filter changes
  };

  // Handle price range change
  const handlePriceChange = (_event: Event, newValue: number | number[]) => {
    setPriceRange(newValue as [number, number]);
    setCurrentPage(1); // Reset to first page when filter changes
  };

  // Handle sort change
  const handleSortChange = (sortValue: string) => {
    setSortBy(sortValue);
    setSortAnchorEl(null);
    setCurrentPage(1); // Reset to first page when sort changes
  };

  // Handle view mode change
  const handleViewModeChange = (_event: React.MouseEvent<HTMLElement>, newMode: 'grid' | 'list' | null) => {
    if (newMode !== null) {
      setViewMode(newMode);
    }
  };

  // Calculate active filters count for badge
  const activeFiltersCount = useMemo(() => {
    let count = 0;
    if (selectedCategories.length > 0) count++;
    if (priceRange[0] > priceRangeBounds[0] || priceRange[1] < priceRangeBounds[1]) count++;
    if (showNewOnly) count++;
    if (showDiscountsOnly) count++;
    return count;
  }, [selectedCategories, priceRange, priceRangeBounds, showNewOnly, showDiscountsOnly]);

  // Sort label
  const sortLabel = useMemo(() => {
    switch (sortBy) {
      case 'price-low':
        return 'Price: Low to High';
      case 'price-high':
        return 'Price: High to Low';
      case 'name-asc':
        return 'Name: A to Z';
      case 'name-desc':
        return 'Name: Z to A';
      case 'rating-high':
        return 'Highest Rated';
      case 'newest':
        return 'Newest First';
      default:
        return 'Sort By';
    }
  }, [sortBy]);
import {
  Box,
  Typography,
  Grid,
  TextField,
  Container,
  Divider,
  Card,
  CardContent,
  InputAdornment,
  FormControl,
  Select,
  MenuItem,
  Chip,
  Stack,
  Pagination,
  ToggleButton,
  ToggleButtonGroup,
  Skeleton,
  Paper,
  Button,
  Tooltip,
  IconButton,
  Menu,
  Popover,
  Slider,
  Badge,
  FormControlLabel,
  Checkbox,
  useTheme,
  useMediaQuery
} from '@mui/material';

// Icons
import SearchIcon from '@mui/icons-material/Search';
import GridViewIcon from '@mui/icons-material/GridView';
import ViewListIcon from '@mui/icons-material/ViewList';
import RefreshIcon from '@mui/icons-material/Refresh';
import TuneIcon from '@mui/icons-material/Tune';
import ClearIcon from '@mui/icons-material/Clear';
import NewReleasesIcon from '@mui/icons-material/NewReleases';
import LocalOfferIcon from '@mui/icons-material/LocalOffer';
import FilterListIcon from '@mui/icons-material/FilterList';
import SortIcon from '@mui/icons-material/Sort';

export default function Products() {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const isTablet = useMediaQuery(theme.breakpoints.down('md'));

  // State variables
  const [products, setProducts] = useState<Product[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [sortBy, setSortBy] = useState<string>('default');
  const [selectedCategories, setSelectedCategories] = useState<string[]>([]);
  const [priceRange, setPriceRange] = useState<[number, number]>([0, 1000]);
  const [showNewOnly, setShowNewOnly] = useState(false);
  const [showDiscountsOnly, setShowDiscountsOnly] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 8;
  
  // Filter popover state
  const [filterAnchorEl, setFilterAnchorEl] = useState<HTMLButtonElement | null>(null);
  const [sortAnchorEl, setSortAnchorEl] = useState<HTMLButtonElement | null>(null);
  const openFilterPopover = Boolean(filterAnchorEl);
  const openSortMenu = Boolean(sortAnchorEl);

  // Extract unique categories from products for filter options
  const categories = useMemo(() => {
    const categorySet = new Set<string>();
    products.forEach((product) => {
      if (product.category) categorySet.add(product.category);
    });
    return Array.from(categorySet);
  }, [products]);

  // Track price range for slider
  const priceMinMax = useMemo(() => {
    if (products.length === 0) return { min: 0, max: 1000 };
    let min = Math.min(...products.map((p) => p.price || 0));
    let max = Math.max(...products.map((p) => p.price || 0));
    // Add padding to max for slider range
    max = Math.ceil(max * 1.2);
    return { min, max };
  }, [products]);

  // Update price range when products change
  useEffect(() => {
    setPriceRange([priceMinMax.min, priceMinMax.max]);
  }, [priceMinMax]);

  // Handle filter changes
  const handleCategoryChange = (category: string) => {
    setSelectedCategories((prev) => (prev.includes(category) ? prev.filter((c) => c !== category) : [...prev, category]));
    setCurrentPage(1);
  };

  const handlePriceChange = (event: Event, newValue: number | number[]) => {
    setPriceRange(newValue as [number, number]);
    setCurrentPage(1);
  };

  const handleSortChange = (event: React.ChangeEvent<{ value: unknown }>) => {
    setSortBy(event.target.value as string);
    setSortAnchorEl(null);
    setCurrentPage(1);
  };
  
  const handleFilterClick = (event: React.MouseEvent<HTMLButtonElement>) => {
    setFilterAnchorEl(event.currentTarget);
  };
  
  const handleSortClick = (event: React.MouseEvent<HTMLButtonElement>) => {
    setSortAnchorEl(event.currentTarget);
  };
  
  const handleCloseFilterPopover = () => {
    setFilterAnchorEl(null);
  };
  
  const handleCloseSortMenu = () => {
    setSortAnchorEl(null);
  };

  const resetFilters = () => {
    setSelectedCategories([]);
    setPriceRange([priceMinMax.min, priceMinMax.max]);
    setShowNewOnly(false);
    setShowDiscountsOnly(false);
    setSearchTerm('');
    setSortBy('default');
    setCurrentPage(1);
  };

  useEffect(() => {
    getProducts().then((data) => {
      setProducts(data);
      setLoading(false);
    });
  }, []);

  // Apply filtering and sorting to products
  const filteredProducts = useMemo(() => {
    return products
      .filter((product) => {
        // Search term filter
        const matchesSearch =
          !searchTerm ||
          product?.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
          (product?.description || '').toLowerCase().includes(searchTerm.toLowerCase());

        // Category filter
        const matchesCategory = selectedCategories.length === 0 || (product.category && selectedCategories.includes(product.category));

        // Price range filter
        const matchesPrice = (product.price || 0) >= priceRange[0] && (product.price || 0) <= priceRange[1];

        // New products filter
        const matchesNew = !showNewOnly || product.isNew === true;

        // Discount products filter
        const matchesDiscount = !showDiscountsOnly || (product.originalPrice && product.originalPrice > product.price);

        return matchesSearch && matchesCategory && matchesPrice && matchesNew && matchesDiscount;
      })
      .sort((a, b) => {
        // Sort results
        switch (sortBy) {
          case 'price-low':
            return (a.price || 0) - (b.price || 0);
          case 'price-high':
            return (b.price || 0) - (a.price || 0);
          case 'name-asc':
            return (a.name || '').localeCompare(b.name || '');
          case 'name-desc':
            return (b.name || '').localeCompare(a.name || '');
          case 'rating':
            return (b.rating || 0) - (a.rating || 0);
          case 'newest':
            return (b.isNew ? 1 : 0) - (a.isNew ? 1 : 0);
          default:
            return 0;
        }
      });
  }, [products, searchTerm, selectedCategories, priceRange, showNewOnly, showDiscountedOnly, sortBy]);

  // Pagination
  const paginatedProducts = useMemo(() => {
    const startIndex = (currentPage - 1) * itemsPerPage;
    return filteredProducts.slice(startIndex, startIndex + itemsPerPage);
  }, [filteredProducts, currentPage, itemsPerPage]);

  return (
    <Container maxWidth="xl" sx={{ py: 4 }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
        <Typography variant="h4" component="h1">
          Products
        </Typography>
        <Box sx={{ display: 'flex', gap: 2 }}>
          <Button
            startIcon={<TuneIcon />}
            variant="contained"
            color="primary"
            sx={{ display: { xs: 'none', md: 'flex' } }}
          >
            Filters
          </Button>
        </Box>
      </Box>

      <Box sx={{ mb: 4 }}>
        <Card>
          <CardContent sx={{ p: 3 }}>
            <Grid container spacing={2}>
              {/* Main content area */}
              <Box sx={{ width: '100%', mb: 4 }}>
                {loading ? (
                  <Box sx={{ display: 'flex', justifyContent: 'center', py: 4 }}>
                    <Box sx={{ width: '100%', display: 'flex', flexDirection: 'column', gap: 2 }}>
                      {Array.from(new Array(6)).map((_, index) => (
                        <Card key={index} sx={{ width: '100%', height: 280 }}>
                          <Skeleton variant="rectangular" width="100%" height={180} />
                          <CardContent>
                            <Skeleton width="60%" height={28} />
                            <Skeleton width="90%" />
                            <Skeleton width="40%" />
                          </CardContent>
                        </Card>
                      ))}
                    </Box>
                  </Box>
                ) : filteredProducts.length === 0 ? (
                  <Paper
                    sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', justifyContent: 'center', p: 4, textAlign: 'center' }}
                  >
                    <Typography variant="h6">No products found</Typography>
                    <Typography variant="body2" color="text.secondary">
                      Try adjusting your filters or search term
                    </Typography>
                    <Button startIcon={<RefreshIcon />} onClick={resetFilters} sx={{ mt: 2 }}>
                      Reset filters
                    </Button>
                  </Paper>
                ) : (
                  <Box>
                    <Grid container spacing={4}>
                      {paginatedProducts.map((product) => (
                        <Grid item xs={12} sm={6} md={viewMode === 'list' ? 12 : 6} lg={viewMode === 'list' ? 12 : 4} key={product.id}>
                          <Box sx={{ 
                            height: '100%',
                            transition: 'transform 0.2s, box-shadow 0.2s',
                            '&:hover': {
                              transform: 'translateY(-4px)',
                              boxShadow: (theme) => theme.shadows[8]
                            }
                          }}>
                            <ProductCard product={product} />
                          </Box>
                        </Grid>
                      ))}
                    </Grid>

                    {/* Results count and pagination */}
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mt: 4 }}>
                      <Typography variant="body2" color="text.secondary">
                        Showing {paginatedProducts.length} of {filteredProducts.length} products
                      </Typography>
                      {filteredProducts.length > itemsPerPage && (
                        <Pagination
                          count={Math.ceil(filteredProducts.length / itemsPerPage)}
                          page={currentPage}
                          onChange={(e, value) => setCurrentPage(value)}
                          color="primary"
                        />
                      )}
                    </Box>
                  </Box>
                )}
              </Box>
            </Grid>
          </CardContent>
        </Card>
        </Box>
      </Container>
    </>
  );
}
              <Typography variant="h6">No products found</Typography>
              <Typography variant="body2" color="text.secondary">
                Try adjusting your filters or search term
              </Typography>
              <Button startIcon={<RefreshIcon />} onClick={resetFilters} sx={{ mt: 2 }}>
                Reset filters
              </Button>
            </Paper>
          ) : (
            <Box>
              <Grid container spacing={4}>
                {paginatedProducts.map((product) => (
                  <Grid item xs={12} sm={6} md={viewMode === 'list' ? 12 : 6} lg={viewMode === 'list' ? 12 : 4} key={product.id}>
                    <Box sx={{ 
                      height: '100%',
                      transition: 'transform 0.2s, box-shadow 0.2s',
                      '&:hover': {
                        transform: 'translateY(-4px)',
                        boxShadow: (theme) => theme.shadows[8]
                      }
                    }}>
                      <ProductCard product={product} />
                    </Box>
                  </Grid>
                ))}
              </Grid>

              {/* Results count and pagination */}
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mt: 4 }}>
                <Typography variant="body2" color="text.secondary">
                  Showing {paginatedProducts.length} of {filteredProducts.length} products
                </Typography>
                {filteredProducts.length > itemsPerPage && (
                  <Pagination
                    count={Math.ceil(filteredProducts.length / itemsPerPage)}
                    page={currentPage}
                    onChange={(e, value) => setCurrentPage(value)}
                    color="primary"
                  />
                )}
              </Box>
            </Box>
          )}
        </Box>
      </Box>
    </Container>
  );
}
