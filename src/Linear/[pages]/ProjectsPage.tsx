/**
 * Linear Projects Page
 * 
 * Main page for managing Linear projects
 */

import React, { useState } from 'react';
import {
  Box,
  Container,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Typography,
  Alert
} from '@mui/material';

import { useLinear } from '../[contexts]/LinearContext';
import ProjectList from '../[components]/ProjectList';
import ProjectForm from '../[components]/ProjectForm';
import ProjectDetails from '../[components]/ProjectDetails';
import { LinearProject } from '../[types]/Linear';
import { linearService } from '../[services]/linearService';

type ViewMode = 'list' | 'details' | 'form';

export default function ProjectsPage() {
  const { isAuthenticated, isLoading, error } = useLinear();
  const [viewMode, setViewMode] = useState<ViewMode>('list');
  const [selectedProject, setSelectedProject] = useState<LinearProject | null>(null);
  const [formMode, setFormMode] = useState<'create' | 'edit'>('create');
  const [showForm, setShowForm] = useState(false);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [deleteLoading, setDeleteLoading] = useState(false);
  const [refreshKey, setRefreshKey] = useState(0);

  const handleProjectSelect = (project: LinearProject) => {
    setSelectedProject(project);
    setViewMode('details');
  };

  const handleProjectCreate = () => {
    setSelectedProject(null);
    setFormMode('create');
    setShowForm(true);
  };

  const handleProjectEdit = (project: LinearProject) => {
    setSelectedProject(project);
    setFormMode('edit');
    setShowForm(true);
  };

  const handleProjectDelete = (project: LinearProject) => {
    setSelectedProject(project);
    setShowDeleteDialog(true);
  };

  const handleFormSubmit = (project: LinearProject) => {
    setShowForm(false);
    setRefreshKey(prev => prev + 1); // Trigger refresh
    
    if (formMode === 'edit' && viewMode === 'details') {
      setSelectedProject(project);
    }
  };

  const handleFormClose = () => {
    setShowForm(false);
    setSelectedProject(null);
  };

  const handleDeleteConfirm = async () => {
    if (!selectedProject) return;

    setDeleteLoading(true);
    try {
      await linearService.deleteProject(selectedProject.id);
      setShowDeleteDialog(false);
      setSelectedProject(null);
      setViewMode('list');
      setRefreshKey(prev => prev + 1); // Trigger refresh
    } catch (error) {
      console.error('Error deleting project:', error);
      // You might want to show an error message here
    } finally {
      setDeleteLoading(false);
    }
  };

  const handleDeleteCancel = () => {
    setShowDeleteDialog(false);
    setSelectedProject(null);
  };

  const handleBackToList = () => {
    setViewMode('list');
    setSelectedProject(null);
  };

  if (isLoading) {
    return (
      <Container maxWidth="lg" sx={{ py: 4 }}>
        <Box display="flex" justifyContent="center" alignItems="center" minHeight={400}>
          <Typography>Loading Linear integration...</Typography>
        </Box>
      </Container>
    );
  }

  if (!isAuthenticated) {
    return (
      <Container maxWidth="lg" sx={{ py: 4 }}>
        <Alert severity="warning">
          Please configure Linear integration in settings to view projects.
        </Alert>
      </Container>
    );
  }

  if (error) {
    return (
      <Container maxWidth="lg" sx={{ py: 4 }}>
        <Alert severity="error">
          {error}
        </Alert>
      </Container>
    );
  }

  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      {/* Main Content */}
      {viewMode === 'list' && (
        <ProjectList
          key={refreshKey}
          onProjectSelect={handleProjectSelect}
          onProjectCreate={handleProjectCreate}
          onProjectEdit={handleProjectEdit}
          onProjectDelete={handleProjectDelete}
        />
      )}

      {viewMode === 'details' && selectedProject && (
        <ProjectDetails
          projectId={selectedProject.id}
          onBack={handleBackToList}
          onEdit={handleProjectEdit}
          onDelete={handleProjectDelete}
        />
      )}

      {/* Project Form Dialog */}
      <ProjectForm
        open={showForm}
        onClose={handleFormClose}
        onSubmit={handleFormSubmit}
        project={selectedProject}
        mode={formMode}
      />

      {/* Delete Confirmation Dialog */}
      <Dialog
        open={showDeleteDialog}
        onClose={handleDeleteCancel}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>
          Delete Project
        </DialogTitle>
        <DialogContent>
          <Typography>
            Are you sure you want to delete the project "{selectedProject?.name}"?
          </Typography>
          <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
            This action cannot be undone. All issues associated with this project will be unassigned.
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleDeleteCancel} disabled={deleteLoading}>
            Cancel
          </Button>
          <Button
            onClick={handleDeleteConfirm}
            color="error"
            variant="contained"
            disabled={deleteLoading}
          >
            {deleteLoading ? 'Deleting...' : 'Delete Project'}
          </Button>
        </DialogActions>
      </Dialog>
    </Container>
  );
}
