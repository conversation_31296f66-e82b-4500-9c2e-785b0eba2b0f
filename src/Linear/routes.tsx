/**
 * Linear Integration Routes
 * 
 * Route definitions for Linear integration pages
 */

import { lazy } from 'react';
import { RouteObject } from 'react-router-dom';
import Loadable from '[components]/Loadable';
import ROUTES from 'Routing/appRoutes';

// Lazy load Linear components
const LinearDashboard = Loadable(lazy(() => import('./[pages]/LinearDashboard')));
const ProjectsPage = Loadable(lazy(() => import('./[pages]/ProjectsPage')));
const IssuesPage = Loadable(lazy(() => import('./[pages]/IssuesPage')));
const LinearSettings = Loadable(lazy(() => import('./[pages]/LinearSettings')));

// Linear routes configuration
const LinearRoutes: RouteObject[] = [
  {
    path: ROUTES.LINEAR.DASHBOARD,
    element: <LinearDashboard />
  },
  {
    path: ROUTES.LINEAR.PROJECTS,
    element: <ProjectsPage />
  },
  {
    path: ROUTES.LINEAR.ISSUES,
    element: <IssuesPage />
  },
  {
    path: ROUTES.LINEAR.SETTINGS,
    element: <LinearSettings />
  }
];

export default LinearRoutes;
