/**
 * Linear Issue Form Component
 * 
 * Form for creating and editing Linear issues
 */

import React, { useState, useEffect } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Button,
  Box,
  Grid,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Chip,
  Autocomplete,
  Typography,
  Alert,
  CircularProgress
} from '@mui/material';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';

import { useLinear } from '../[contexts]/LinearContext';
import { linearService } from '../[services]/linearService';
import {
  LinearIssue,
  LinearProject,
  LinearUser,
  LinearWorkflowState,
  LinearIssueLabel,
  IssuePriority,
  CreateIssueInput,
  UpdateIssueInput
} from '../[types]/Linear';

interface IssueFormProps {
  open: boolean;
  onClose: () => void;
  onSubmit: (issue: LinearIssue) => void;
  issue?: LinearIssue | null;
  mode: 'create' | 'edit';
  defaultProjectId?: string;
}

interface FormData {
  title: string;
  description: string;
  priority: IssuePriority;
  estimate: number | null;
  assigneeId: string;
  projectId: string;
  stateId: string;
  labelIds: string[];
  parentId: string;
  dueDate: Date | null;
}

export default function IssueForm({
  open,
  onClose,
  onSubmit,
  issue,
  mode,
  defaultProjectId
}: IssueFormProps) {
  const { selectedTeamId, isAuthenticated } = useLinear();
  const [formData, setFormData] = useState<FormData>({
    title: '',
    description: '',
    priority: IssuePriority.NO_PRIORITY,
    estimate: null,
    assigneeId: '',
    projectId: defaultProjectId || '',
    stateId: '',
    labelIds: [],
    parentId: '',
    dueDate: null
  });
  
  const [projects, setProjects] = useState<LinearProject[]>([]);
  const [users, setUsers] = useState<LinearUser[]>([]);
  const [workflowStates, setWorkflowStates] = useState<LinearWorkflowState[]>([]);
  const [labels, setLabels] = useState<LinearIssueLabel[]>([]);
  const [parentIssues, setParentIssues] = useState<LinearIssue[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [loadingData, setLoadingData] = useState(false);

  // Load form data when dialog opens
  useEffect(() => {
    if (open && isAuthenticated && selectedTeamId) {
      loadFormData();
    }
  }, [open, isAuthenticated, selectedTeamId]);

  // Initialize form data when issue changes
  useEffect(() => {
    if (issue && mode === 'edit') {
      setFormData({
        title: issue.title,
        description: issue.description || '',
        priority: issue.priority,
        estimate: issue.estimate || null,
        assigneeId: issue.assignee?.id || '',
        projectId: issue.project?.id || '',
        stateId: issue.state.id,
        labelIds: issue.labels.map(label => label.id),
        parentId: issue.parent?.id || '',
        dueDate: issue.dueDate || null
      });
    } else if (mode === 'create') {
      setFormData({
        title: '',
        description: '',
        priority: IssuePriority.NO_PRIORITY,
        estimate: null,
        assigneeId: '',
        projectId: defaultProjectId || '',
        stateId: '',
        labelIds: [],
        parentId: '',
        dueDate: null
      });
    }
  }, [issue, mode, defaultProjectId]);

  const loadFormData = async () => {
    setLoadingData(true);
    try {
      const [projectList, userList, stateList, labelList, issueList] = await Promise.all([
        linearService.getProjects({ teamIds: [selectedTeamId!] }),
        linearService.getUsers(),
        linearService.getWorkflowStates(selectedTeamId!),
        linearService.getLabels(selectedTeamId!),
        linearService.getIssues({ teamId: selectedTeamId! })
      ]);
      
      setProjects(projectList);
      setUsers(userList.filter(user => user.active));
      setWorkflowStates(stateList);
      setLabels(labelList);
      setParentIssues(issueList.filter(i => i.id !== issue?.id)); // Exclude current issue from parent options
    } catch (err) {
      console.error('Error loading form data:', err);
      setError('Failed to load form data');
    } finally {
      setLoadingData(false);
    }
  };

  const handleInputChange = (field: keyof FormData) => (
    event: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement> | any
  ) => {
    const value = event.target ? event.target.value : event;
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleDateChange = (date: Date | null) => {
    setFormData(prev => ({
      ...prev,
      dueDate: date
    }));
  };

  const handleSubmit = async (event: React.FormEvent) => {
    event.preventDefault();
    if (!selectedTeamId) return;

    setLoading(true);
    setError(null);

    try {
      let result: LinearIssue;

      if (mode === 'create') {
        const input: CreateIssueInput = {
          title: formData.title,
          description: formData.description || undefined,
          teamId: selectedTeamId,
          assigneeId: formData.assigneeId || undefined,
          projectId: formData.projectId || undefined,
          priority: formData.priority,
          estimate: formData.estimate || undefined,
          labelIds: formData.labelIds,
          stateId: formData.stateId || undefined,
          parentId: formData.parentId || undefined,
          dueDate: formData.dueDate || undefined
        };
        result = await linearService.createIssue(input);
      } else {
        const input: UpdateIssueInput = {
          id: issue!.id,
          title: formData.title,
          description: formData.description || undefined,
          assigneeId: formData.assigneeId || undefined,
          projectId: formData.projectId || undefined,
          priority: formData.priority,
          estimate: formData.estimate || undefined,
          labelIds: formData.labelIds,
          stateId: formData.stateId || undefined,
          parentId: formData.parentId || undefined,
          dueDate: formData.dueDate || undefined
        };
        result = await linearService.updateIssue(input);
      }

      onSubmit(result);
      onClose();
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to save issue');
    } finally {
      setLoading(false);
    }
  };

  const isFormValid = () => {
    return formData.title.trim().length > 0;
  };

  const getPriorityLabel = (priority: IssuePriority): string => {
    switch (priority) {
      case IssuePriority.URGENT:
        return 'Urgent';
      case IssuePriority.HIGH:
        return 'High';
      case IssuePriority.MEDIUM:
        return 'Medium';
      case IssuePriority.LOW:
        return 'Low';
      default:
        return 'No Priority';
    }
  };

  return (
    <Dialog open={open} onClose={onClose} maxWidth="md" fullWidth>
      <form onSubmit={handleSubmit}>
        <DialogTitle>
          {mode === 'create' ? 'Create New Issue' : 'Edit Issue'}
        </DialogTitle>

        <DialogContent>
          <LocalizationProvider dateAdapter={AdapterDateFns}>
            <Box sx={{ mt: 2 }}>
              {error && (
                <Alert severity="error" sx={{ mb: 2 }}>
                  {error}
                </Alert>
              )}

              {loadingData && (
                <Box display="flex" justifyContent="center" py={2}>
                  <CircularProgress />
                </Box>
              )}

              <Grid container spacing={3}>
                {/* Basic Information */}
                <Grid item xs={12}>
                  <Typography variant="h6" gutterBottom>
                    Basic Information
                  </Typography>
                </Grid>

                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    label="Issue Title"
                    value={formData.title}
                    onChange={handleInputChange('title')}
                    required
                    error={!formData.title.trim()}
                    helperText={!formData.title.trim() ? 'Issue title is required' : ''}
                  />
                </Grid>

                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    label="Description"
                    value={formData.description}
                    onChange={handleInputChange('description')}
                    multiline
                    rows={4}
                    placeholder="Describe the issue..."
                  />
                </Grid>

                {/* Assignment and Project */}
                <Grid item xs={12}>
                  <Typography variant="h6" gutterBottom>
                    Assignment and Project
                  </Typography>
                </Grid>

                <Grid item xs={12} md={6}>
                  <Autocomplete
                    options={users}
                    getOptionLabel={(user) => user.displayName}
                    value={users.find(user => user.id === formData.assigneeId) || null}
                    onChange={(_, selectedUser) => {
                      setFormData(prev => ({
                        ...prev,
                        assigneeId: selectedUser?.id || ''
                      }));
                    }}
                    loading={loadingData}
                    renderInput={(params) => (
                      <TextField
                        {...params}
                        label="Assignee"
                        InputProps={{
                          ...params.InputProps,
                          endAdornment: (
                            <>
                              {loadingData ? <CircularProgress color="inherit" size={20} /> : null}
                              {params.InputProps.endAdornment}
                            </>
                          ),
                        }}
                      />
                    )}
                  />
                </Grid>

                <Grid item xs={12} md={6}>
                  <Autocomplete
                    options={projects}
                    getOptionLabel={(project) => project.name}
                    value={projects.find(project => project.id === formData.projectId) || null}
                    onChange={(_, selectedProject) => {
                      setFormData(prev => ({
                        ...prev,
                        projectId: selectedProject?.id || ''
                      }));
                    }}
                    loading={loadingData}
                    renderInput={(params) => (
                      <TextField
                        {...params}
                        label="Project"
                        InputProps={{
                          ...params.InputProps,
                          endAdornment: (
                            <>
                              {loadingData ? <CircularProgress color="inherit" size={20} /> : null}
                              {params.InputProps.endAdornment}
                            </>
                          ),
                        }}
                      />
                    )}
                  />
                </Grid>

                {/* Priority and State */}
                <Grid item xs={12}>
                  <Typography variant="h6" gutterBottom>
                    Priority and Status
                  </Typography>
                </Grid>

                <Grid item xs={12} md={4}>
                  <FormControl fullWidth>
                    <InputLabel>Priority</InputLabel>
                    <Select
                      value={formData.priority}
                      onChange={handleInputChange('priority')}
                      label="Priority"
                    >
                      {Object.values(IssuePriority).filter(p => typeof p === 'number').map((priority) => (
                        <MenuItem key={priority} value={priority}>
                          {getPriorityLabel(priority as IssuePriority)}
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                </Grid>

                <Grid item xs={12} md={4}>
                  <Autocomplete
                    options={workflowStates}
                    getOptionLabel={(state) => state.name}
                    value={workflowStates.find(state => state.id === formData.stateId) || null}
                    onChange={(_, selectedState) => {
                      setFormData(prev => ({
                        ...prev,
                        stateId: selectedState?.id || ''
                      }));
                    }}
                    loading={loadingData}
                    renderInput={(params) => (
                      <TextField
                        {...params}
                        label="State"
                        InputProps={{
                          ...params.InputProps,
                          endAdornment: (
                            <>
                              {loadingData ? <CircularProgress color="inherit" size={20} /> : null}
                              {params.InputProps.endAdornment}
                            </>
                          ),
                        }}
                      />
                    )}
                  />
                </Grid>

                <Grid item xs={12} md={4}>
                  <TextField
                    fullWidth
                    label="Estimate (points)"
                    type="number"
                    value={formData.estimate || ''}
                    onChange={(e) => {
                      const value = e.target.value ? parseInt(e.target.value) : null;
                      setFormData(prev => ({ ...prev, estimate: value }));
                    }}
                    inputProps={{ min: 0, max: 100 }}
                  />
                </Grid>

                {/* Labels and Relationships */}
                <Grid item xs={12}>
                  <Typography variant="h6" gutterBottom>
                    Labels and Relationships
                  </Typography>
                </Grid>

                <Grid item xs={12} md={6}>
                  <Autocomplete
                    multiple
                    options={labels}
                    getOptionLabel={(label) => label.name}
                    value={labels.filter(label => formData.labelIds.includes(label.id))}
                    onChange={(_, selectedLabels) => {
                      setFormData(prev => ({
                        ...prev,
                        labelIds: selectedLabels.map(label => label.id)
                      }));
                    }}
                    loading={loadingData}
                    renderInput={(params) => (
                      <TextField
                        {...params}
                        label="Labels"
                        InputProps={{
                          ...params.InputProps,
                          endAdornment: (
                            <>
                              {loadingData ? <CircularProgress color="inherit" size={20} /> : null}
                              {params.InputProps.endAdornment}
                            </>
                          ),
                        }}
                      />
                    )}
                    renderTags={(value, getTagProps) =>
                      value.map((label, index) => (
                        <Chip
                          key={label.id}
                          label={label.name}
                          {...getTagProps({ index })}
                          size="small"
                          sx={{ backgroundColor: label.color + '40', borderColor: label.color }}
                        />
                      ))
                    }
                  />
                </Grid>

                <Grid item xs={12} md={6}>
                  <Autocomplete
                    options={parentIssues}
                    getOptionLabel={(issue) => `${issue.identifier}: ${issue.title}`}
                    value={parentIssues.find(issue => issue.id === formData.parentId) || null}
                    onChange={(_, selectedIssue) => {
                      setFormData(prev => ({
                        ...prev,
                        parentId: selectedIssue?.id || ''
                      }));
                    }}
                    loading={loadingData}
                    renderInput={(params) => (
                      <TextField
                        {...params}
                        label="Parent Issue"
                        InputProps={{
                          ...params.InputProps,
                          endAdornment: (
                            <>
                              {loadingData ? <CircularProgress color="inherit" size={20} /> : null}
                              {params.InputProps.endAdornment}
                            </>
                          ),
                        }}
                      />
                    )}
                  />
                </Grid>

                {/* Due Date */}
                <Grid item xs={12} md={6}>
                  <DatePicker
                    label="Due Date"
                    value={formData.dueDate}
                    onChange={handleDateChange}
                    slotProps={{
                      textField: {
                        fullWidth: true
                      }
                    }}
                  />
                </Grid>
              </Grid>
            </Box>
          </LocalizationProvider>
        </DialogContent>

        <DialogActions>
          <Button onClick={onClose} disabled={loading}>
            Cancel
          </Button>
          <Button
            type="submit"
            variant="contained"
            disabled={!isFormValid() || loading || loadingData}
          >
            {loading ? (
              <CircularProgress size={20} />
            ) : (
              mode === 'create' ? 'Create Issue' : 'Update Issue'
            )}
          </Button>
        </DialogActions>
      </form>
    </Dialog>
  );
}
