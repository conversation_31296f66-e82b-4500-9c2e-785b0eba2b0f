/**
 * Linear Issue List Component
 * 
 * Displays a list of Linear issues with filtering and actions
 */

import React, { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Chip,
  Button,
  Grid,
  TextField,
  InputAdornment,
  IconButton,
  Menu,
  MenuItem,
  LinearProgress,
  Avatar,
  Tooltip,
  Stack,
  Alert,
  FormControl,
  InputLabel,
  Select,
  Autocomplete
} from '@mui/material';
import {
  Search as SearchIcon,
  FilterList as FilterIcon,
  Add as AddIcon,
  MoreVert as MoreVertIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Visibility as ViewIcon,
  Assignment as AssignmentIcon,
  Flag as FlagIcon,
  Schedule as ScheduleIcon
} from '@mui/icons-material';

import { useLinear } from '../[contexts]/LinearContext';
import { linearService } from '../[services]/linearService';
import {
  LinearIssue,
  LinearProject,
  LinearUser,
  LinearWorkflowState,
  IssueFilter,
  IssuePriority,
  WorkflowStateType
} from '../[types]/Linear';

interface IssueListProps {
  projectId?: string;
  onIssueSelect?: (issue: LinearIssue) => void;
  onIssueCreate?: () => void;
  onIssueEdit?: (issue: LinearIssue) => void;
  onIssueDelete?: (issue: LinearIssue) => void;
}

export default function IssueList({
  projectId,
  onIssueSelect,
  onIssueCreate,
  onIssueEdit,
  onIssueDelete
}: IssueListProps) {
  const { isAuthenticated, selectedTeamId } = useLinear();
  const [issues, setIssues] = useState<LinearIssue[]>([]);
  const [projects, setProjects] = useState<LinearProject[]>([]);
  const [users, setUsers] = useState<LinearUser[]>([]);
  const [workflowStates, setWorkflowStates] = useState<LinearWorkflowState[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [filter, setFilter] = useState<IssueFilter>({});
  const [showFilters, setShowFilters] = useState(false);
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [selectedIssue, setSelectedIssue] = useState<LinearIssue | null>(null);

  // Load data
  useEffect(() => {
    if (isAuthenticated && selectedTeamId) {
      loadIssues();
      loadFilterData();
    }
  }, [isAuthenticated, selectedTeamId, filter, projectId]);

  const loadIssues = async () => {
    setLoading(true);
    setError(null);
    try {
      const issueFilter: IssueFilter = {
        ...filter,
        ...(selectedTeamId && { teamId: selectedTeamId }),
        ...(projectId && { projectId }),
        ...(searchQuery && { search: searchQuery })
      };
      
      const issueList = await linearService.getIssues(issueFilter);
      setIssues(issueList);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load issues');
    } finally {
      setLoading(false);
    }
  };

  const loadFilterData = async () => {
    try {
      const [projectList, userList, stateList] = await Promise.all([
        linearService.getProjects({ teamIds: selectedTeamId ? [selectedTeamId] : undefined }),
        linearService.getUsers(),
        selectedTeamId ? linearService.getWorkflowStates(selectedTeamId) : Promise.resolve([])
      ]);
      
      setProjects(projectList);
      setUsers(userList.filter(user => user.active));
      setWorkflowStates(stateList);
    } catch (err) {
      console.error('Error loading filter data:', err);
    }
  };

  const handleSearch = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSearchQuery(event.target.value);
  };

  const handleFilterChange = (field: keyof IssueFilter, value: any) => {
    setFilter(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const clearFilters = () => {
    setFilter({});
    setSearchQuery('');
  };

  const handleMenuOpen = (event: React.MouseEvent<HTMLElement>, issue: LinearIssue) => {
    setAnchorEl(event.currentTarget);
    setSelectedIssue(issue);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
    setSelectedIssue(null);
  };

  const handleIssueAction = (action: 'view' | 'edit' | 'delete') => {
    if (!selectedIssue) return;

    switch (action) {
      case 'view':
        onIssueSelect?.(selectedIssue);
        break;
      case 'edit':
        onIssueEdit?.(selectedIssue);
        break;
      case 'delete':
        onIssueDelete?.(selectedIssue);
        break;
    }
    handleMenuClose();
  };

  const getPriorityColor = (priority: IssuePriority): string => {
    switch (priority) {
      case IssuePriority.URGENT:
        return 'error';
      case IssuePriority.HIGH:
        return 'warning';
      case IssuePriority.MEDIUM:
        return 'info';
      case IssuePriority.LOW:
        return 'success';
      default:
        return 'default';
    }
  };

  const getPriorityLabel = (priority: IssuePriority): string => {
    switch (priority) {
      case IssuePriority.URGENT:
        return 'Urgent';
      case IssuePriority.HIGH:
        return 'High';
      case IssuePriority.MEDIUM:
        return 'Medium';
      case IssuePriority.LOW:
        return 'Low';
      default:
        return 'No Priority';
    }
  };

  const formatDate = (date: Date): string => {
    return new Intl.DateTimeFormat('en-US', {
      month: 'short',
      day: 'numeric'
    }).format(date);
  };

  if (!isAuthenticated) {
    return (
      <Alert severity="warning">
        Please authenticate with Linear to view issues.
      </Alert>
    );
  }

  if (!selectedTeamId) {
    return (
      <Alert severity="info">
        Please select a team to view issues.
      </Alert>
    );
  }

  return (
    <Box>
      {/* Header */}
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Typography variant="h4" component="h1">
          Linear Issues
        </Typography>
        <Button
          variant="contained"
          startIcon={<AddIcon />}
          onClick={onIssueCreate}
        >
          New Issue
        </Button>
      </Box>

      {/* Search and Filters */}
      <Box mb={3}>
        <Grid container spacing={2} alignItems="center">
          <Grid item xs={12} md={6}>
            <TextField
              fullWidth
              placeholder="Search issues..."
              value={searchQuery}
              onChange={handleSearch}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <SearchIcon />
                  </InputAdornment>
                )
              }}
            />
          </Grid>
          <Grid item xs={12} md={6}>
            <Box display="flex" gap={1} justifyContent="flex-end">
              <Button
                variant="outlined"
                startIcon={<FilterIcon />}
                onClick={() => setShowFilters(!showFilters)}
              >
                Filters
              </Button>
              {(Object.keys(filter).length > 0 || searchQuery) && (
                <Button variant="outlined" onClick={clearFilters}>
                  Clear
                </Button>
              )}
            </Box>
          </Grid>
        </Grid>

        {/* Filter Panel */}
        {showFilters && (
          <Card sx={{ mt: 2, p: 2 }}>
            <Grid container spacing={2}>
              <Grid item xs={12} md={3}>
                <Autocomplete
                  options={projects}
                  getOptionLabel={(project) => project.name}
                  value={projects.find(p => p.id === filter.projectId) || null}
                  onChange={(_, project) => handleFilterChange('projectId', project?.id)}
                  renderInput={(params) => (
                    <TextField {...params} label="Project" size="small" />
                  )}
                />
              </Grid>
              <Grid item xs={12} md={3}>
                <Autocomplete
                  options={users}
                  getOptionLabel={(user) => user.displayName}
                  value={users.find(u => u.id === filter.assigneeId) || null}
                  onChange={(_, user) => handleFilterChange('assigneeId', user?.id)}
                  renderInput={(params) => (
                    <TextField {...params} label="Assignee" size="small" />
                  )}
                />
              </Grid>
              <Grid item xs={12} md={3}>
                <FormControl fullWidth size="small">
                  <InputLabel>Priority</InputLabel>
                  <Select
                    value={filter.priority ?? ''}
                    onChange={(e) => handleFilterChange('priority', e.target.value || undefined)}
                    label="Priority"
                  >
                    <MenuItem value="">All</MenuItem>
                    <MenuItem value={IssuePriority.URGENT}>Urgent</MenuItem>
                    <MenuItem value={IssuePriority.HIGH}>High</MenuItem>
                    <MenuItem value={IssuePriority.MEDIUM}>Medium</MenuItem>
                    <MenuItem value={IssuePriority.LOW}>Low</MenuItem>
                    <MenuItem value={IssuePriority.NO_PRIORITY}>No Priority</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12} md={3}>
                <FormControl fullWidth size="small">
                  <InputLabel>State</InputLabel>
                  <Select
                    value={filter.state ?? ''}
                    onChange={(e) => handleFilterChange('state', e.target.value || undefined)}
                    label="State"
                  >
                    <MenuItem value="">All</MenuItem>
                    <MenuItem value={WorkflowStateType.BACKLOG}>Backlog</MenuItem>
                    <MenuItem value={WorkflowStateType.UNSTARTED}>Unstarted</MenuItem>
                    <MenuItem value={WorkflowStateType.STARTED}>Started</MenuItem>
                    <MenuItem value={WorkflowStateType.COMPLETED}>Completed</MenuItem>
                    <MenuItem value={WorkflowStateType.CANCELED}>Canceled</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
            </Grid>
          </Card>
        )}
      </Box>

      {/* Loading */}
      {loading && <LinearProgress sx={{ mb: 2 }} />}

      {/* Error */}
      {error && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
      )}

      {/* Issues List */}
      <Stack spacing={2}>
        {issues.map((issue) => (
          <Card
            key={issue.id}
            sx={{
              cursor: 'pointer',
              '&:hover': {
                boxShadow: 2
              }
            }}
            onClick={() => onIssueSelect?.(issue)}
          >
            <CardContent>
              <Box display="flex" justifyContent="between" alignItems="flex-start">
                <Box flexGrow={1}>
                  {/* Issue Header */}
                  <Box display="flex" alignItems="center" gap={1} mb={1}>
                    <Typography variant="body2" color="text.secondary" fontFamily="monospace">
                      {issue.identifier}
                    </Typography>
                    <Chip
                      label={issue.state.name}
                      size="small"
                      sx={{ backgroundColor: issue.state.color, color: 'white' }}
                    />
                    <Chip
                      label={getPriorityLabel(issue.priority)}
                      size="small"
                      color={getPriorityColor(issue.priority) as any}
                    />
                  </Box>

                  {/* Issue Title */}
                  <Typography variant="h6" component="h3" mb={1}>
                    {issue.title}
                  </Typography>

                  {/* Issue Meta */}
                  <Box display="flex" alignItems="center" gap={2} flexWrap="wrap">
                    {issue.assignee && (
                      <Box display="flex" alignItems="center" gap={0.5}>
                        <Avatar
                          src={issue.assignee.avatarUrl}
                          sx={{ width: 20, height: 20 }}
                        >
                          {issue.assignee.displayName.charAt(0)}
                        </Avatar>
                        <Typography variant="body2" color="text.secondary">
                          {issue.assignee.displayName}
                        </Typography>
                      </Box>
                    )}
                    
                    {issue.project && (
                      <Box display="flex" alignItems="center" gap={0.5}>
                        <AssignmentIcon fontSize="small" color="action" />
                        <Typography variant="body2" color="text.secondary">
                          {issue.project.name}
                        </Typography>
                      </Box>
                    )}

                    {issue.estimate && (
                      <Box display="flex" alignItems="center" gap={0.5}>
                        <ScheduleIcon fontSize="small" color="action" />
                        <Typography variant="body2" color="text.secondary">
                          {issue.estimate} pts
                        </Typography>
                      </Box>
                    )}

                    <Typography variant="body2" color="text.secondary">
                      Created {formatDate(issue.createdAt)}
                    </Typography>
                  </Box>

                  {/* Labels */}
                  {issue.labels.length > 0 && (
                    <Box mt={1}>
                      <Stack direction="row" spacing={0.5} flexWrap="wrap">
                        {issue.labels.map((label) => (
                          <Chip
                            key={label.id}
                            label={label.name}
                            size="small"
                            variant="outlined"
                            sx={{ backgroundColor: label.color + '20', borderColor: label.color }}
                          />
                        ))}
                      </Stack>
                    </Box>
                  )}
                </Box>

                <IconButton
                  size="small"
                  onClick={(e) => {
                    e.stopPropagation();
                    handleMenuOpen(e, issue);
                  }}
                >
                  <MoreVertIcon />
                </IconButton>
              </Box>
            </CardContent>
          </Card>
        ))}
      </Stack>

      {/* Empty State */}
      {!loading && issues.length === 0 && (
        <Box textAlign="center" py={8}>
          <Typography variant="h6" color="text.secondary" mb={2}>
            No issues found
          </Typography>
          <Typography variant="body2" color="text.secondary" mb={3}>
            {searchQuery || Object.keys(filter).length > 0 
              ? 'Try adjusting your search criteria' 
              : 'Create your first issue to get started'
            }
          </Typography>
          {!searchQuery && Object.keys(filter).length === 0 && (
            <Button
              variant="contained"
              startIcon={<AddIcon />}
              onClick={onIssueCreate}
            >
              Create Issue
            </Button>
          )}
        </Box>
      )}

      {/* Context Menu */}
      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleMenuClose}
      >
        <MenuItem onClick={() => handleIssueAction('view')}>
          <ViewIcon sx={{ mr: 1 }} />
          View Details
        </MenuItem>
        <MenuItem onClick={() => handleIssueAction('edit')}>
          <EditIcon sx={{ mr: 1 }} />
          Edit Issue
        </MenuItem>
        <MenuItem onClick={() => handleIssueAction('delete')}>
          <DeleteIcon sx={{ mr: 1 }} />
          Delete Issue
        </MenuItem>
      </Menu>
    </Box>
  );
}
