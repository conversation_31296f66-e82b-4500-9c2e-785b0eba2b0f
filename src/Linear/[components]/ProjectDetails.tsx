/**
 * Linear Project Details Component
 * 
 * Displays detailed information about a Linear project
 */

import React, { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Chip,
  Button,
  Grid,
  LinearProgress,
  Avatar,
  AvatarGroup,
  Tooltip,
  Stack,
  Divider,
  IconButton,
  Alert,
  CircularProgress
} from '@mui/material';
import {
  Edit as EditIcon,
  Delete as DeleteIcon,
  ArrowBack as ArrowBackIcon,
  CalendarToday as CalendarIcon,
  Group as TeamIcon,
  TrendingUp as ProgressIcon,
  Assignment as IssueIcon,
  Flag as FlagIcon
} from '@mui/icons-material';

import { useLinear } from '../[contexts]/LinearContext';
import { linearService } from '../[services]/linearService';
import {
  LinearProject,
  LinearIssue,
  ProjectState
} from '../[types]/Linear';

interface ProjectDetailsProps {
  projectId: string;
  onBack: () => void;
  onEdit: (project: LinearProject) => void;
  onDelete: (project: LinearProject) => void;
}

export default function ProjectDetails({
  projectId,
  onBack,
  onEdit,
  onDelete
}: ProjectDetailsProps) {
  const { isAuthenticated } = useLinear();
  const [project, setProject] = useState<LinearProject | null>(null);
  const [issues, setIssues] = useState<LinearIssue[]>([]);
  const [loading, setLoading] = useState(true);
  const [loadingIssues, setLoadingIssues] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (isAuthenticated && projectId) {
      loadProject();
      loadProjectIssues();
    }
  }, [isAuthenticated, projectId]);

  const loadProject = async () => {
    setLoading(true);
    setError(null);
    try {
      const projectData = await linearService.getProject(projectId);
      if (projectData) {
        setProject(projectData);
      } else {
        setError('Project not found');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load project');
    } finally {
      setLoading(false);
    }
  };

  const loadProjectIssues = async () => {
    setLoadingIssues(true);
    try {
      const issueList = await linearService.getIssues({ projectId });
      setIssues(issueList);
    } catch (err) {
      console.error('Error loading project issues:', err);
    } finally {
      setLoadingIssues(false);
    }
  };

  const getStateColor = (state: ProjectState): string => {
    switch (state) {
      case ProjectState.BACKLOG:
        return 'default';
      case ProjectState.PLANNED:
        return 'info';
      case ProjectState.STARTED:
        return 'primary';
      case ProjectState.PAUSED:
        return 'warning';
      case ProjectState.COMPLETED:
        return 'success';
      case ProjectState.CANCELED:
        return 'error';
      default:
        return 'default';
    }
  };

  const formatDate = (date?: Date): string => {
    if (!date) return 'Not set';
    return new Intl.DateTimeFormat('en-US', {
      month: 'long',
      day: 'numeric',
      year: 'numeric'
    }).format(date);
  };

  const getPriorityColor = (priority: number): string => {
    if (priority <= 2) return 'error';
    if (priority <= 4) return 'warning';
    if (priority <= 6) return 'info';
    return 'success';
  };

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight={400}>
        <CircularProgress />
      </Box>
    );
  }

  if (error || !project) {
    return (
      <Alert severity="error">
        {error || 'Project not found'}
      </Alert>
    );
  }

  return (
    <Box>
      {/* Header */}
      <Box display="flex" alignItems="center" mb={3}>
        <IconButton onClick={onBack} sx={{ mr: 2 }}>
          <ArrowBackIcon />
        </IconButton>
        <Box flexGrow={1}>
          <Typography variant="h4" component="h1">
            {project.icon} {project.name}
          </Typography>
          <Chip
            label={project.state}
            color={getStateColor(project.state) as any}
            sx={{ mt: 1 }}
          />
        </Box>
        <Stack direction="row" spacing={1}>
          <Button
            variant="outlined"
            startIcon={<EditIcon />}
            onClick={() => onEdit(project)}
          >
            Edit
          </Button>
          <Button
            variant="outlined"
            color="error"
            startIcon={<DeleteIcon />}
            onClick={() => onDelete(project)}
          >
            Delete
          </Button>
        </Stack>
      </Box>

      <Grid container spacing={3}>
        {/* Main Information */}
        <Grid item xs={12} md={8}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Project Overview
              </Typography>
              
              {project.description && (
                <Typography variant="body1" paragraph>
                  {project.description}
                </Typography>
              )}

              <Divider sx={{ my: 2 }} />

              {/* Progress */}
              <Box mb={3}>
                <Box display="flex" justifyContent="space-between" alignItems="center" mb={1}>
                  <Typography variant="h6">
                    Progress
                  </Typography>
                  <Typography variant="h6" fontWeight="medium">
                    {Math.round(project.progress * 100)}%
                  </Typography>
                </Box>
                <LinearProgress
                  variant="determinate"
                  value={project.progress * 100}
                  sx={{ height: 8, borderRadius: 4 }}
                />
                <Typography variant="body2" color="text.secondary" mt={1}>
                  {project.completedIssueCount} of {project.issueCount} issues completed
                </Typography>
              </Box>

              <Divider sx={{ my: 2 }} />

              {/* Key Metrics */}
              <Grid container spacing={3}>
                <Grid item xs={6} md={3}>
                  <Box textAlign="center">
                    <IssueIcon color="primary" sx={{ fontSize: 32, mb: 1 }} />
                    <Typography variant="h4" fontWeight="bold">
                      {project.issueCount}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Total Issues
                    </Typography>
                  </Box>
                </Grid>
                <Grid item xs={6} md={3}>
                  <Box textAlign="center">
                    <ProgressIcon color="success" sx={{ fontSize: 32, mb: 1 }} />
                    <Typography variant="h4" fontWeight="bold">
                      {project.completedIssueCount}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Completed
                    </Typography>
                  </Box>
                </Grid>
                <Grid item xs={6} md={3}>
                  <Box textAlign="center">
                    <TeamIcon color="info" sx={{ fontSize: 32, mb: 1 }} />
                    <Typography variant="h4" fontWeight="bold">
                      {project.teams.length}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Teams
                    </Typography>
                  </Box>
                </Grid>
                <Grid item xs={6} md={3}>
                  <Box textAlign="center">
                    <FlagIcon color={getPriorityColor(project.priority) as any} sx={{ fontSize: 32, mb: 1 }} />
                    <Typography variant="h4" fontWeight="bold">
                      {project.priority}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Priority
                    </Typography>
                  </Box>
                </Grid>
              </Grid>
            </CardContent>
          </Card>

          {/* Recent Issues */}
          <Card sx={{ mt: 3 }}>
            <CardContent>
              <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
                <Typography variant="h6">
                  Recent Issues
                </Typography>
                {loadingIssues && <CircularProgress size={20} />}
              </Box>
              
              {issues.length > 0 ? (
                <Stack spacing={2}>
                  {issues.slice(0, 5).map((issue) => (
                    <Box
                      key={issue.id}
                      sx={{
                        p: 2,
                        border: '1px solid',
                        borderColor: 'divider',
                        borderRadius: 1,
                        '&:hover': {
                          backgroundColor: 'action.hover'
                        }
                      }}
                    >
                      <Box display="flex" justifyContent="space-between" alignItems="flex-start">
                        <Box flexGrow={1}>
                          <Typography variant="subtitle1" fontWeight="medium">
                            {issue.identifier}: {issue.title}
                          </Typography>
                          <Box display="flex" alignItems="center" gap={1} mt={1}>
                            <Chip
                              label={issue.state.name}
                              size="small"
                              sx={{ backgroundColor: issue.state.color, color: 'white' }}
                            />
                            {issue.assignee && (
                              <Tooltip title={issue.assignee.displayName}>
                                <Avatar
                                  src={issue.assignee.avatarUrl}
                                  sx={{ width: 24, height: 24 }}
                                >
                                  {issue.assignee.displayName.charAt(0)}
                                </Avatar>
                              </Tooltip>
                            )}
                          </Box>
                        </Box>
                        <Typography variant="body2" color="text.secondary">
                          {formatDate(issue.createdAt)}
                        </Typography>
                      </Box>
                    </Box>
                  ))}
                </Stack>
              ) : (
                <Typography variant="body2" color="text.secondary" textAlign="center" py={4}>
                  No issues found for this project
                </Typography>
              )}
            </CardContent>
          </Card>
        </Grid>

        {/* Sidebar */}
        <Grid item xs={12} md={4}>
          {/* Project Details */}
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Project Details
              </Typography>
              
              <Stack spacing={2}>
                {/* Lead */}
                {project.lead && (
                  <Box>
                    <Typography variant="body2" color="text.secondary" gutterBottom>
                      Project Lead
                    </Typography>
                    <Box display="flex" alignItems="center" gap={1}>
                      <Avatar
                        src={project.lead.avatarUrl}
                        sx={{ width: 32, height: 32 }}
                      >
                        {project.lead.displayName.charAt(0)}
                      </Avatar>
                      <Typography variant="body1">
                        {project.lead.displayName}
                      </Typography>
                    </Box>
                  </Box>
                )}

                {/* Dates */}
                <Box>
                  <Typography variant="body2" color="text.secondary" gutterBottom>
                    Timeline
                  </Typography>
                  <Stack spacing={1}>
                    <Box display="flex" justifyContent="space-between">
                      <Typography variant="body2">Start Date:</Typography>
                      <Typography variant="body2" fontWeight="medium">
                        {formatDate(project.startDate)}
                      </Typography>
                    </Box>
                    <Box display="flex" justifyContent="space-between">
                      <Typography variant="body2">Target Date:</Typography>
                      <Typography variant="body2" fontWeight="medium">
                        {formatDate(project.targetDate)}
                      </Typography>
                    </Box>
                    {project.completedAt && (
                      <Box display="flex" justifyContent="space-between">
                        <Typography variant="body2">Completed:</Typography>
                        <Typography variant="body2" fontWeight="medium">
                          {formatDate(project.completedAt)}
                        </Typography>
                      </Box>
                    )}
                  </Stack>
                </Box>

                {/* Teams */}
                <Box>
                  <Typography variant="body2" color="text.secondary" gutterBottom>
                    Teams ({project.teams.length})
                  </Typography>
                  <Stack spacing={1}>
                    {project.teams.map((team) => (
                      <Chip
                        key={team.id}
                        label={team.name}
                        size="small"
                        variant="outlined"
                      />
                    ))}
                  </Stack>
                </Box>

                {/* Team Members */}
                {project.members.length > 0 && (
                  <Box>
                    <Typography variant="body2" color="text.secondary" gutterBottom>
                      Team Members ({project.members.length})
                    </Typography>
                    <AvatarGroup max={8}>
                      {project.members.map((member) => (
                        <Tooltip key={member.id} title={member.displayName}>
                          <Avatar
                            src={member.avatarUrl}
                            alt={member.displayName}
                            sx={{ width: 32, height: 32 }}
                          >
                            {member.displayName.charAt(0)}
                          </Avatar>
                        </Tooltip>
                      ))}
                    </AvatarGroup>
                  </Box>
                )}
              </Stack>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );
}
