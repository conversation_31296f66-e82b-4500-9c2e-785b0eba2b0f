/**
 * Linear Project Form Component
 * 
 * Form for creating and editing Linear projects
 */

import React, { useState, useEffect } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Button,
  Box,
  Grid,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Chip,
  Autocomplete,
  Typography,
  Alert,
  CircularProgress
} from '@mui/material';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';

import { useLinear } from '../[contexts]/LinearContext';
import { linearService } from '../[services]/linearService';
import {
  LinearProject,
  LinearTeam,
  LinearUser,
  ProjectState,
  CreateProjectInput,
  UpdateProjectInput
} from '../[types]/Linear';

interface ProjectFormProps {
  open: boolean;
  onClose: () => void;
  onSubmit: (project: LinearProject) => void;
  project?: LinearProject | null;
  mode: 'create' | 'edit';
}

interface FormData {
  name: string;
  description: string;
  state: ProjectState;
  priority: number;
  startDate: Date | null;
  targetDate: Date | null;
  leadId: string;
  memberIds: string[];
  teamIds: string[];
  color: string;
  icon: string;
}

const PROJECT_COLORS = [
  '#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7',
  '#DDA0DD', '#98D8C8', '#F7DC6F', '#BB8FCE', '#85C1E9'
];

const PROJECT_ICONS = [
  '🚀', '📊', '🎯', '💡', '🔧', '📱', '🌟', '🎨', '📈', '🔬'
];

export default function ProjectForm({
  open,
  onClose,
  onSubmit,
  project,
  mode
}: ProjectFormProps) {
  const { teams, isAuthenticated } = useLinear();
  const [formData, setFormData] = useState<FormData>({
    name: '',
    description: '',
    state: ProjectState.PLANNED,
    priority: 1,
    startDate: null,
    targetDate: null,
    leadId: '',
    memberIds: [],
    teamIds: [],
    color: PROJECT_COLORS[0],
    icon: PROJECT_ICONS[0]
  });
  const [users, setUsers] = useState<LinearUser[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [loadingUsers, setLoadingUsers] = useState(false);

  // Load users when dialog opens
  useEffect(() => {
    if (open && isAuthenticated) {
      loadUsers();
    }
  }, [open, isAuthenticated]);

  // Initialize form data when project changes
  useEffect(() => {
    if (project && mode === 'edit') {
      setFormData({
        name: project.name,
        description: project.description || '',
        state: project.state,
        priority: project.priority,
        startDate: project.startDate || null,
        targetDate: project.targetDate || null,
        leadId: project.lead?.id || '',
        memberIds: project.members.map(member => member.id),
        teamIds: project.teams.map(team => team.id),
        color: project.color || PROJECT_COLORS[0],
        icon: project.icon || PROJECT_ICONS[0]
      });
    } else if (mode === 'create') {
      setFormData({
        name: '',
        description: '',
        state: ProjectState.PLANNED,
        priority: 1,
        startDate: null,
        targetDate: null,
        leadId: '',
        memberIds: [],
        teamIds: [],
        color: PROJECT_COLORS[0],
        icon: PROJECT_ICONS[0]
      });
    }
  }, [project, mode]);

  const loadUsers = async () => {
    setLoadingUsers(true);
    try {
      const userList = await linearService.getUsers();
      setUsers(userList.filter(user => user.active));
    } catch (err) {
      console.error('Error loading users:', err);
    } finally {
      setLoadingUsers(false);
    }
  };

  const handleInputChange = (field: keyof FormData) => (
    event: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement> | any
  ) => {
    const value = event.target ? event.target.value : event;
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleDateChange = (field: 'startDate' | 'targetDate') => (date: Date | null) => {
    setFormData(prev => ({
      ...prev,
      [field]: date
    }));
  };

  const handleSubmit = async (event: React.FormEvent) => {
    event.preventDefault();
    setLoading(true);
    setError(null);

    try {
      let result: LinearProject;

      if (mode === 'create') {
        const input: CreateProjectInput = {
          name: formData.name,
          description: formData.description || undefined,
          teamIds: formData.teamIds,
          leadId: formData.leadId || undefined,
          memberIds: formData.memberIds,
          state: formData.state,
          priority: formData.priority,
          startDate: formData.startDate || undefined,
          targetDate: formData.targetDate || undefined,
          color: formData.color,
          icon: formData.icon
        };
        result = await linearService.createProject(input);
      } else {
        const input: UpdateProjectInput = {
          id: project!.id,
          name: formData.name,
          description: formData.description || undefined,
          teamIds: formData.teamIds,
          leadId: formData.leadId || undefined,
          memberIds: formData.memberIds,
          state: formData.state,
          priority: formData.priority,
          startDate: formData.startDate || undefined,
          targetDate: formData.targetDate || undefined,
          color: formData.color,
          icon: formData.icon
        };
        result = await linearService.updateProject(input);
      }

      onSubmit(result);
      onClose();
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to save project');
    } finally {
      setLoading(false);
    }
  };

  const isFormValid = () => {
    return formData.name.trim().length > 0 && formData.teamIds.length > 0;
  };

  return (
    <Dialog open={open} onClose={onClose} maxWidth="md" fullWidth>
      <form onSubmit={handleSubmit}>
        <DialogTitle>
          {mode === 'create' ? 'Create New Project' : 'Edit Project'}
        </DialogTitle>

        <DialogContent>
          <LocalizationProvider dateAdapter={AdapterDateFns}>
            <Box sx={{ mt: 2 }}>
              {error && (
                <Alert severity="error" sx={{ mb: 2 }}>
                  {error}
                </Alert>
              )}

              <Grid container spacing={3}>
                {/* Basic Information */}
                <Grid item xs={12}>
                  <Typography variant="h6" gutterBottom>
                    Basic Information
                  </Typography>
                </Grid>

                <Grid item xs={12} md={8}>
                  <TextField
                    fullWidth
                    label="Project Name"
                    value={formData.name}
                    onChange={handleInputChange('name')}
                    required
                    error={!formData.name.trim()}
                    helperText={!formData.name.trim() ? 'Project name is required' : ''}
                  />
                </Grid>

                <Grid item xs={12} md={4}>
                  <FormControl fullWidth>
                    <InputLabel>State</InputLabel>
                    <Select
                      value={formData.state}
                      onChange={handleInputChange('state')}
                      label="State"
                    >
                      {Object.values(ProjectState).map((state) => (
                        <MenuItem key={state} value={state}>
                          {state.charAt(0).toUpperCase() + state.slice(1)}
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                </Grid>

                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    label="Description"
                    value={formData.description}
                    onChange={handleInputChange('description')}
                    multiline
                    rows={3}
                  />
                </Grid>

                {/* Teams and Members */}
                <Grid item xs={12}>
                  <Typography variant="h6" gutterBottom>
                    Teams and Members
                  </Typography>
                </Grid>

                <Grid item xs={12} md={6}>
                  <Autocomplete
                    multiple
                    options={teams}
                    getOptionLabel={(team) => team.name}
                    value={teams.filter(team => formData.teamIds.includes(team.id))}
                    onChange={(_, selectedTeams) => {
                      setFormData(prev => ({
                        ...prev,
                        teamIds: selectedTeams.map(team => team.id)
                      }));
                    }}
                    renderInput={(params) => (
                      <TextField
                        {...params}
                        label="Teams"
                        required
                        error={formData.teamIds.length === 0}
                        helperText={formData.teamIds.length === 0 ? 'At least one team is required' : ''}
                      />
                    )}
                    renderTags={(value, getTagProps) =>
                      value.map((team, index) => (
                        <Chip
                          key={team.id}
                          label={team.name}
                          {...getTagProps({ index })}
                          size="small"
                        />
                      ))
                    }
                  />
                </Grid>

                <Grid item xs={12} md={6}>
                  <Autocomplete
                    options={users}
                    getOptionLabel={(user) => user.displayName}
                    value={users.find(user => user.id === formData.leadId) || null}
                    onChange={(_, selectedUser) => {
                      setFormData(prev => ({
                        ...prev,
                        leadId: selectedUser?.id || ''
                      }));
                    }}
                    loading={loadingUsers}
                    renderInput={(params) => (
                      <TextField
                        {...params}
                        label="Project Lead"
                        InputProps={{
                          ...params.InputProps,
                          endAdornment: (
                            <>
                              {loadingUsers ? <CircularProgress color="inherit" size={20} /> : null}
                              {params.InputProps.endAdornment}
                            </>
                          ),
                        }}
                      />
                    )}
                  />
                </Grid>

                <Grid item xs={12}>
                  <Autocomplete
                    multiple
                    options={users}
                    getOptionLabel={(user) => user.displayName}
                    value={users.filter(user => formData.memberIds.includes(user.id))}
                    onChange={(_, selectedUsers) => {
                      setFormData(prev => ({
                        ...prev,
                        memberIds: selectedUsers.map(user => user.id)
                      }));
                    }}
                    loading={loadingUsers}
                    renderInput={(params) => (
                      <TextField
                        {...params}
                        label="Team Members"
                        InputProps={{
                          ...params.InputProps,
                          endAdornment: (
                            <>
                              {loadingUsers ? <CircularProgress color="inherit" size={20} /> : null}
                              {params.InputProps.endAdornment}
                            </>
                          ),
                        }}
                      />
                    )}
                    renderTags={(value, getTagProps) =>
                      value.map((user, index) => (
                        <Chip
                          key={user.id}
                          label={user.displayName}
                          {...getTagProps({ index })}
                          size="small"
                        />
                      ))
                    }
                  />
                </Grid>

                {/* Dates and Priority */}
                <Grid item xs={12}>
                  <Typography variant="h6" gutterBottom>
                    Timeline and Priority
                  </Typography>
                </Grid>

                <Grid item xs={12} md={4}>
                  <DatePicker
                    label="Start Date"
                    value={formData.startDate}
                    onChange={handleDateChange('startDate')}
                    slotProps={{
                      textField: {
                        fullWidth: true
                      }
                    }}
                  />
                </Grid>

                <Grid item xs={12} md={4}>
                  <DatePicker
                    label="Target Date"
                    value={formData.targetDate}
                    onChange={handleDateChange('targetDate')}
                    slotProps={{
                      textField: {
                        fullWidth: true
                      }
                    }}
                  />
                </Grid>

                <Grid item xs={12} md={4}>
                  <TextField
                    fullWidth
                    label="Priority"
                    type="number"
                    value={formData.priority}
                    onChange={handleInputChange('priority')}
                    inputProps={{ min: 1, max: 10 }}
                  />
                </Grid>

                {/* Appearance */}
                <Grid item xs={12}>
                  <Typography variant="h6" gutterBottom>
                    Appearance
                  </Typography>
                </Grid>

                <Grid item xs={12} md={6}>
                  <Typography variant="body2" gutterBottom>
                    Color
                  </Typography>
                  <Box display="flex" gap={1} flexWrap="wrap">
                    {PROJECT_COLORS.map((color) => (
                      <Box
                        key={color}
                        sx={{
                          width: 32,
                          height: 32,
                          backgroundColor: color,
                          borderRadius: 1,
                          cursor: 'pointer',
                          border: formData.color === color ? '3px solid #000' : '1px solid #ccc'
                        }}
                        onClick={() => setFormData(prev => ({ ...prev, color }))}
                      />
                    ))}
                  </Box>
                </Grid>

                <Grid item xs={12} md={6}>
                  <Typography variant="body2" gutterBottom>
                    Icon
                  </Typography>
                  <Box display="flex" gap={1} flexWrap="wrap">
                    {PROJECT_ICONS.map((icon) => (
                      <Box
                        key={icon}
                        sx={{
                          width: 32,
                          height: 32,
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center',
                          fontSize: '16px',
                          cursor: 'pointer',
                          border: formData.icon === icon ? '2px solid #1976d2' : '1px solid #ccc',
                          borderRadius: 1
                        }}
                        onClick={() => setFormData(prev => ({ ...prev, icon }))}
                      >
                        {icon}
                      </Box>
                    ))}
                  </Box>
                </Grid>
              </Grid>
            </Box>
          </LocalizationProvider>
        </DialogContent>

        <DialogActions>
          <Button onClick={onClose} disabled={loading}>
            Cancel
          </Button>
          <Button
            type="submit"
            variant="contained"
            disabled={!isFormValid() || loading}
          >
            {loading ? (
              <CircularProgress size={20} />
            ) : (
              mode === 'create' ? 'Create Project' : 'Update Project'
            )}
          </Button>
        </DialogActions>
      </form>
    </Dialog>
  );
}
