/**
 * Linear Integration Types
 * 
 * TypeScript interfaces for Linear entities and API responses
 */

// Base Linear entity interface
export interface LinearEntity {
  id: string;
  createdAt: Date;
  updatedAt: Date;
}

// Linear User
export interface LinearUser extends LinearEntity {
  name: string;
  displayName: string;
  email: string;
  avatarUrl?: string;
  isMe: boolean;
  active: boolean;
  admin: boolean;
  guest: boolean;
}

// Linear Team
export interface LinearTeam extends LinearEntity {
  name: string;
  key: string;
  description?: string;
  color?: string;
  icon?: string;
  private: boolean;
  issueEstimationType: string;
  issueEstimationAllowZero: boolean;
  issueEstimationExtended: boolean;
  issueOrderingNoPriorityFirst: boolean;
  issueCount: number;
  activeCycleCount: number;
  draftWorkflowState?: LinearWorkflowState;
  startWorkflowState?: LinearWorkflowState;
  reviewWorkflowState?: LinearWorkflowState;
  mergeWorkflowState?: LinearWorkflowState;
}

// Linear Project
export interface LinearProject extends LinearEntity {
  name: string;
  description?: string;
  color?: string;
  icon?: string;
  state: ProjectState;
  priority: number;
  progress: number;
  scope: number;
  sortOrder: number;
  slugId: string;
  url: string;
  startDate?: Date;
  targetDate?: Date;
  completedAt?: Date;
  canceledAt?: Date;
  lead?: LinearUser;
  members: LinearUser[];
  teams: LinearTeam[];
  issueCount: number;
  completedIssueCount: number;
  scopeHistory: ProjectScopeHistory[];
  projectMilestones: LinearProjectMilestone[];
}

// Linear Issue
export interface LinearIssue extends LinearEntity {
  identifier: string;
  number: number;
  title: string;
  description?: string;
  priority: IssuePriority;
  estimate?: number;
  sortOrder: number;
  boardOrder: number;
  url: string;
  branchName: string;
  customerTicketCount: number;
  cycle?: LinearCycle;
  project?: LinearProject;
  team: LinearTeam;
  assignee?: LinearUser;
  creator: LinearUser;
  state: LinearWorkflowState;
  parent?: LinearIssue;
  labels: LinearIssueLabel[];
  comments: LinearComment[];
  attachments: LinearAttachment[];
  relations: LinearIssueRelation[];
  subscribers: LinearUser[];
  dueDate?: Date;
  snoozedUntilAt?: Date;
  completedAt?: Date;
  canceledAt?: Date;
  autoClosedAt?: Date;
  autoArchivedAt?: Date;
  triagedAt?: Date;
  lastAppliedTemplateAt?: Date;
}

// Linear Workflow State
export interface LinearWorkflowState extends LinearEntity {
  name: string;
  color: string;
  description?: string;
  position: number;
  type: WorkflowStateType;
  team: LinearTeam;
}

// Linear Issue Label
export interface LinearIssueLabel extends LinearEntity {
  name: string;
  color: string;
  description?: string;
  team?: LinearTeam;
  parent?: LinearIssueLabel;
  children: LinearIssueLabel[];
}

// Linear Comment
export interface LinearComment extends LinearEntity {
  body: string;
  edited: boolean;
  issue: LinearIssue;
  user: LinearUser;
  parent?: LinearComment;
  children: LinearComment[];
  reactionData: ReactionData[];
}

// Linear Attachment
export interface LinearAttachment extends LinearEntity {
  title: string;
  subtitle?: string;
  url: string;
  metadata: Record<string, any>;
  source?: AttachmentSource;
  sourceType?: AttachmentSourceType;
  creator: LinearUser;
  issue?: LinearIssue;
}

// Linear Cycle
export interface LinearCycle extends LinearEntity {
  number: number;
  name?: string;
  description?: string;
  startsAt: Date;
  endsAt: Date;
  completedAt?: Date;
  progress: number;
  team: LinearTeam;
  issues: LinearIssue[];
  uncompletedIssuesUponClose: LinearIssue[];
}

// Linear Project Milestone
export interface LinearProjectMilestone extends LinearEntity {
  name: string;
  description?: string;
  targetDate?: Date;
  sortOrder: number;
  project: LinearProject;
}

// Linear Issue Relation
export interface LinearIssueRelation extends LinearEntity {
  type: IssueRelationType;
  issue: LinearIssue;
  relatedIssue: LinearIssue;
}

// Enums and Types
export enum ProjectState {
  BACKLOG = 'backlog',
  PLANNED = 'planned',
  STARTED = 'started',
  PAUSED = 'paused',
  COMPLETED = 'completed',
  CANCELED = 'canceled'
}

export enum IssuePriority {
  NO_PRIORITY = 0,
  URGENT = 1,
  HIGH = 2,
  MEDIUM = 3,
  LOW = 4
}

export enum WorkflowStateType {
  BACKLOG = 'backlog',
  UNSTARTED = 'unstarted',
  STARTED = 'started',
  COMPLETED = 'completed',
  CANCELED = 'canceled'
}

export enum IssueRelationType {
  BLOCKS = 'blocks',
  BLOCKED_BY = 'blockedBy',
  DUPLICATE = 'duplicate',
  DUPLICATED_BY = 'duplicatedBy',
  RELATES = 'relates'
}

export enum AttachmentSourceType {
  GITHUB = 'github',
  SLACK = 'slack',
  FIGMA = 'figma',
  URL = 'url'
}

// Supporting Types
export interface ProjectScopeHistory {
  date: Date;
  scope: number;
}

export interface ReactionData {
  emoji: string;
  count: number;
  users: LinearUser[];
}

export interface AttachmentSource {
  type: AttachmentSourceType;
  id?: string;
  imageUrl?: string;
}

// API Response Types
export interface LinearConnection<T> {
  nodes: T[];
  pageInfo: PageInfo;
  totalCount?: number;
}

export interface PageInfo {
  hasNextPage: boolean;
  hasPreviousPage: boolean;
  startCursor?: string;
  endCursor?: string;
}

// Linear API Configuration
export interface LinearConfig {
  apiKey: string;
  apiUrl?: string;
  teamId?: string;
  workspaceId?: string;
}

// Linear Integration Settings
export interface LinearIntegrationSettings {
  enabled: boolean;
  apiKey?: string;
  selectedTeamId?: string;
  selectedWorkspaceId?: string;
  syncEnabled: boolean;
  webhookUrl?: string;
  defaultProjectId?: string;
  autoCreateIssues: boolean;
  autoAssignUsers: boolean;
  syncLabels: boolean;
  syncComments: boolean;
  lastSyncAt?: Date;
}

// Create/Update Input Types
export interface CreateIssueInput {
  title: string;
  description?: string;
  teamId: string;
  assigneeId?: string;
  projectId?: string;
  priority?: IssuePriority;
  estimate?: number;
  labelIds?: string[];
  stateId?: string;
  parentId?: string;
  dueDate?: Date;
}

export interface UpdateIssueInput {
  id: string;
  title?: string;
  description?: string;
  assigneeId?: string;
  projectId?: string;
  priority?: IssuePriority;
  estimate?: number;
  labelIds?: string[];
  stateId?: string;
  parentId?: string;
  dueDate?: Date;
}

export interface CreateProjectInput {
  name: string;
  description?: string;
  teamIds: string[];
  leadId?: string;
  memberIds?: string[];
  state?: ProjectState;
  priority?: number;
  startDate?: Date;
  targetDate?: Date;
  color?: string;
  icon?: string;
}

export interface UpdateProjectInput {
  id: string;
  name?: string;
  description?: string;
  teamIds?: string[];
  leadId?: string;
  memberIds?: string[];
  state?: ProjectState;
  priority?: number;
  startDate?: Date;
  targetDate?: Date;
  color?: string;
  icon?: string;
}

// Filter and Sort Types
export interface IssueFilter {
  teamId?: string;
  projectId?: string;
  assigneeId?: string;
  creatorId?: string;
  state?: WorkflowStateType;
  priority?: IssuePriority;
  labelIds?: string[];
  search?: string;
  hasProject?: boolean;
  hasAssignee?: boolean;
  completedAt?: DateFilter;
  createdAt?: DateFilter;
  updatedAt?: DateFilter;
}

export interface ProjectFilter {
  teamIds?: string[];
  leadId?: string;
  memberIds?: string[];
  state?: ProjectState;
  search?: string;
  startDate?: DateFilter;
  targetDate?: DateFilter;
  completedAt?: DateFilter;
}

export interface DateFilter {
  gte?: Date;
  lte?: Date;
  eq?: Date;
}

export interface SortOptions {
  field: string;
  direction: 'ASC' | 'DESC';
}

// Webhook Types
export interface LinearWebhookPayload {
  action: WebhookAction;
  type: WebhookType;
  data: any;
  organizationId: string;
  webhookTimestamp: number;
  webhookId: string;
}

export enum WebhookAction {
  CREATE = 'create',
  UPDATE = 'update',
  REMOVE = 'remove'
}

export enum WebhookType {
  ISSUE = 'Issue',
  PROJECT = 'Project',
  COMMENT = 'Comment',
  CYCLE = 'Cycle',
  PROJECT_UPDATE = 'ProjectUpdate'
}
