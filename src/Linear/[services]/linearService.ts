/**
 * Linear Service
 * 
 * Service layer for Linear API integration
 */

import { LinearClient } from '@linear/sdk';
import {
  LinearConfig,
  LinearUser,
  LinearTeam,
  LinearProject,
  LinearIssue,
  LinearWorkflowState,
  LinearIssueLabel,
  LinearComment,
  LinearCycle,
  CreateIssueInput,
  UpdateIssueInput,
  CreateProjectInput,
  UpdateProjectInput,
  IssueFilter,
  ProjectFilter,
  SortOptions,
  LinearConnection,
  LinearIntegrationSettings
} from '../[types]/Linear';

export class LinearService {
  private client: LinearClient | null = null;
  private config: LinearConfig | null = null;

  /**
   * Initialize Linear client with API key
   */
  async initialize(config: LinearConfig): Promise<void> {
    this.config = config;
    this.client = new LinearClient({
      apiKey: config.apiKey,
      apiUrl: config.apiUrl
    });

    // Test the connection
    try {
      await this.getCurrentUser();
    } catch (error) {
      throw new Error(`Failed to initialize Linear client: ${error}`);
    }
  }

  /**
   * Check if client is initialized
   */
  private ensureInitialized(): void {
    if (!this.client || !this.config) {
      throw new Error('Linear client not initialized. Call initialize() first.');
    }
  }

  /**
   * Get current authenticated user
   */
  async getCurrentUser(): Promise<LinearUser> {
    this.ensureInitialized();
    const viewer = await this.client!.viewer;
    return this.mapUser(viewer);
  }

  /**
   * Get all teams
   */
  async getTeams(): Promise<LinearTeam[]> {
    this.ensureInitialized();
    const teams = await this.client!.teams();
    return teams.nodes.map(team => this.mapTeam(team));
  }

  /**
   * Get team by ID
   */
  async getTeam(teamId: string): Promise<LinearTeam | null> {
    this.ensureInitialized();
    try {
      const team = await this.client!.team(teamId);
      return this.mapTeam(team);
    } catch (error) {
      console.error('Error fetching team:', error);
      return null;
    }
  }

  /**
   * Get all projects
   */
  async getProjects(filter?: ProjectFilter): Promise<LinearProject[]> {
    this.ensureInitialized();
    const projects = await this.client!.projects({
      filter: this.buildProjectFilter(filter)
    });
    return projects.nodes.map(project => this.mapProject(project));
  }

  /**
   * Get project by ID
   */
  async getProject(projectId: string): Promise<LinearProject | null> {
    this.ensureInitialized();
    try {
      const project = await this.client!.project(projectId);
      return this.mapProject(project);
    } catch (error) {
      console.error('Error fetching project:', error);
      return null;
    }
  }

  /**
   * Create a new project
   */
  async createProject(input: CreateProjectInput): Promise<LinearProject> {
    this.ensureInitialized();
    const result = await this.client!.createProject(input);
    if (!result.success) {
      throw new Error(`Failed to create project: ${result.error}`);
    }
    return this.getProject(result.project!.id)!;
  }

  /**
   * Update a project
   */
  async updateProject(input: UpdateProjectInput): Promise<LinearProject> {
    this.ensureInitialized();
    const result = await this.client!.updateProject(input.id, input);
    if (!result.success) {
      throw new Error(`Failed to update project: ${result.error}`);
    }
    return this.getProject(input.id)!;
  }

  /**
   * Delete a project
   */
  async deleteProject(projectId: string): Promise<boolean> {
    this.ensureInitialized();
    const result = await this.client!.deleteProject(projectId);
    return result.success;
  }

  /**
   * Get issues with filtering and sorting
   */
  async getIssues(filter?: IssueFilter, sort?: SortOptions): Promise<LinearIssue[]> {
    this.ensureInitialized();
    const issues = await this.client!.issues({
      filter: this.buildIssueFilter(filter),
      orderBy: sort ? { [sort.field]: sort.direction } : undefined
    });
    return issues.nodes.map(issue => this.mapIssue(issue));
  }

  /**
   * Get issue by ID
   */
  async getIssue(issueId: string): Promise<LinearIssue | null> {
    this.ensureInitialized();
    try {
      const issue = await this.client!.issue(issueId);
      return this.mapIssue(issue);
    } catch (error) {
      console.error('Error fetching issue:', error);
      return null;
    }
  }

  /**
   * Create a new issue
   */
  async createIssue(input: CreateIssueInput): Promise<LinearIssue> {
    this.ensureInitialized();
    const result = await this.client!.createIssue(input);
    if (!result.success) {
      throw new Error(`Failed to create issue: ${result.error}`);
    }
    return this.getIssue(result.issue!.id)!;
  }

  /**
   * Update an issue
   */
  async updateIssue(input: UpdateIssueInput): Promise<LinearIssue> {
    this.ensureInitialized();
    const result = await this.client!.updateIssue(input.id, input);
    if (!result.success) {
      throw new Error(`Failed to update issue: ${result.error}`);
    }
    return this.getIssue(input.id)!;
  }

  /**
   * Delete an issue
   */
  async deleteIssue(issueId: string): Promise<boolean> {
    this.ensureInitialized();
    const result = await this.client!.deleteIssue(issueId);
    return result.success;
  }

  /**
   * Get workflow states for a team
   */
  async getWorkflowStates(teamId: string): Promise<LinearWorkflowState[]> {
    this.ensureInitialized();
    const team = await this.client!.team(teamId);
    const states = await team.states();
    return states.nodes.map(state => this.mapWorkflowState(state));
  }

  /**
   * Get labels for a team
   */
  async getLabels(teamId?: string): Promise<LinearIssueLabel[]> {
    this.ensureInitialized();
    const labels = await this.client!.issueLabels({
      filter: teamId ? { team: { id: { eq: teamId } } } : undefined
    });
    return labels.nodes.map(label => this.mapLabel(label));
  }

  /**
   * Get users in the organization
   */
  async getUsers(): Promise<LinearUser[]> {
    this.ensureInitialized();
    const users = await this.client!.users();
    return users.nodes.map(user => this.mapUser(user));
  }

  /**
   * Get cycles for a team
   */
  async getCycles(teamId: string): Promise<LinearCycle[]> {
    this.ensureInitialized();
    const team = await this.client!.team(teamId);
    const cycles = await team.cycles();
    return cycles.nodes.map(cycle => this.mapCycle(cycle));
  }

  /**
   * Search issues
   */
  async searchIssues(query: string, teamId?: string): Promise<LinearIssue[]> {
    this.ensureInitialized();
    const issues = await this.client!.issues({
      filter: {
        title: { containsIgnoreCase: query },
        ...(teamId && { team: { id: { eq: teamId } } })
      }
    });
    return issues.nodes.map(issue => this.mapIssue(issue));
  }

  /**
   * Get comments for an issue
   */
  async getIssueComments(issueId: string): Promise<LinearComment[]> {
    this.ensureInitialized();
    const issue = await this.client!.issue(issueId);
    const comments = await issue.comments();
    return comments.nodes.map(comment => this.mapComment(comment));
  }

  /**
   * Add comment to an issue
   */
  async addComment(issueId: string, body: string): Promise<LinearComment> {
    this.ensureInitialized();
    const result = await this.client!.createComment({
      issueId,
      body
    });
    if (!result.success) {
      throw new Error(`Failed to add comment: ${result.error}`);
    }
    const comments = await this.getIssueComments(issueId);
    return comments[comments.length - 1]; // Return the latest comment
  }

  // Private mapping methods
  private mapUser(user: any): LinearUser {
    return {
      id: user.id,
      name: user.name,
      displayName: user.displayName,
      email: user.email,
      avatarUrl: user.avatarUrl,
      isMe: user.isMe,
      active: user.active,
      admin: user.admin,
      guest: user.guest,
      createdAt: new Date(user.createdAt),
      updatedAt: new Date(user.updatedAt)
    };
  }

  private mapTeam(team: any): LinearTeam {
    return {
      id: team.id,
      name: team.name,
      key: team.key,
      description: team.description,
      color: team.color,
      icon: team.icon,
      private: team.private,
      issueEstimationType: team.issueEstimationType,
      issueEstimationAllowZero: team.issueEstimationAllowZero,
      issueEstimationExtended: team.issueEstimationExtended,
      issueOrderingNoPriorityFirst: team.issueOrderingNoPriorityFirst,
      issueCount: team.issueCount,
      activeCycleCount: team.activeCycleCount,
      createdAt: new Date(team.createdAt),
      updatedAt: new Date(team.updatedAt)
    };
  }

  private mapProject(project: any): LinearProject {
    return {
      id: project.id,
      name: project.name,
      description: project.description,
      color: project.color,
      icon: project.icon,
      state: project.state,
      priority: project.priority,
      progress: project.progress,
      scope: project.scope,
      sortOrder: project.sortOrder,
      slugId: project.slugId,
      url: project.url,
      startDate: project.startDate ? new Date(project.startDate) : undefined,
      targetDate: project.targetDate ? new Date(project.targetDate) : undefined,
      completedAt: project.completedAt ? new Date(project.completedAt) : undefined,
      canceledAt: project.canceledAt ? new Date(project.canceledAt) : undefined,
      lead: project.lead ? this.mapUser(project.lead) : undefined,
      members: project.members?.nodes?.map((user: any) => this.mapUser(user)) || [],
      teams: project.teams?.nodes?.map((team: any) => this.mapTeam(team)) || [],
      issueCount: project.issueCount,
      completedIssueCount: project.completedIssueCount,
      scopeHistory: project.scopeHistory || [],
      projectMilestones: project.projectMilestones?.nodes || [],
      createdAt: new Date(project.createdAt),
      updatedAt: new Date(project.updatedAt)
    };
  }

  private mapIssue(issue: any): LinearIssue {
    return {
      id: issue.id,
      identifier: issue.identifier,
      number: issue.number,
      title: issue.title,
      description: issue.description,
      priority: issue.priority,
      estimate: issue.estimate,
      sortOrder: issue.sortOrder,
      boardOrder: issue.boardOrder,
      url: issue.url,
      branchName: issue.branchName,
      customerTicketCount: issue.customerTicketCount,
      cycle: issue.cycle ? this.mapCycle(issue.cycle) : undefined,
      project: issue.project ? this.mapProject(issue.project) : undefined,
      team: this.mapTeam(issue.team),
      assignee: issue.assignee ? this.mapUser(issue.assignee) : undefined,
      creator: this.mapUser(issue.creator),
      state: this.mapWorkflowState(issue.state),
      parent: issue.parent ? this.mapIssue(issue.parent) : undefined,
      labels: issue.labels?.nodes?.map((label: any) => this.mapLabel(label)) || [],
      comments: issue.comments?.nodes?.map((comment: any) => this.mapComment(comment)) || [],
      attachments: issue.attachments?.nodes || [],
      relations: issue.relations?.nodes || [],
      subscribers: issue.subscribers?.nodes?.map((user: any) => this.mapUser(user)) || [],
      dueDate: issue.dueDate ? new Date(issue.dueDate) : undefined,
      snoozedUntilAt: issue.snoozedUntilAt ? new Date(issue.snoozedUntilAt) : undefined,
      completedAt: issue.completedAt ? new Date(issue.completedAt) : undefined,
      canceledAt: issue.canceledAt ? new Date(issue.canceledAt) : undefined,
      autoClosedAt: issue.autoClosedAt ? new Date(issue.autoClosedAt) : undefined,
      autoArchivedAt: issue.autoArchivedAt ? new Date(issue.autoArchivedAt) : undefined,
      triagedAt: issue.triagedAt ? new Date(issue.triagedAt) : undefined,
      lastAppliedTemplateAt: issue.lastAppliedTemplateAt ? new Date(issue.lastAppliedTemplateAt) : undefined,
      createdAt: new Date(issue.createdAt),
      updatedAt: new Date(issue.updatedAt)
    };
  }

  private mapWorkflowState(state: any): LinearWorkflowState {
    return {
      id: state.id,
      name: state.name,
      color: state.color,
      description: state.description,
      position: state.position,
      type: state.type,
      team: this.mapTeam(state.team),
      createdAt: new Date(state.createdAt),
      updatedAt: new Date(state.updatedAt)
    };
  }

  private mapLabel(label: any): LinearIssueLabel {
    return {
      id: label.id,
      name: label.name,
      color: label.color,
      description: label.description,
      team: label.team ? this.mapTeam(label.team) : undefined,
      parent: label.parent ? this.mapLabel(label.parent) : undefined,
      children: label.children?.nodes?.map((child: any) => this.mapLabel(child)) || [],
      createdAt: new Date(label.createdAt),
      updatedAt: new Date(label.updatedAt)
    };
  }

  private mapComment(comment: any): LinearComment {
    return {
      id: comment.id,
      body: comment.body,
      edited: comment.edited,
      issue: this.mapIssue(comment.issue),
      user: this.mapUser(comment.user),
      parent: comment.parent ? this.mapComment(comment.parent) : undefined,
      children: comment.children?.nodes?.map((child: any) => this.mapComment(child)) || [],
      reactionData: comment.reactionData || [],
      createdAt: new Date(comment.createdAt),
      updatedAt: new Date(comment.updatedAt)
    };
  }

  private mapCycle(cycle: any): LinearCycle {
    return {
      id: cycle.id,
      number: cycle.number,
      name: cycle.name,
      description: cycle.description,
      startsAt: new Date(cycle.startsAt),
      endsAt: new Date(cycle.endsAt),
      completedAt: cycle.completedAt ? new Date(cycle.completedAt) : undefined,
      progress: cycle.progress,
      team: this.mapTeam(cycle.team),
      issues: cycle.issues?.nodes?.map((issue: any) => this.mapIssue(issue)) || [],
      uncompletedIssuesUponClose: cycle.uncompletedIssuesUponClose?.nodes?.map((issue: any) => this.mapIssue(issue)) || [],
      createdAt: new Date(cycle.createdAt),
      updatedAt: new Date(cycle.updatedAt)
    };
  }

  // Filter builders
  private buildIssueFilter(filter?: IssueFilter): any {
    if (!filter) return undefined;

    const linearFilter: any = {};

    if (filter.teamId) linearFilter.team = { id: { eq: filter.teamId } };
    if (filter.projectId) linearFilter.project = { id: { eq: filter.projectId } };
    if (filter.assigneeId) linearFilter.assignee = { id: { eq: filter.assigneeId } };
    if (filter.creatorId) linearFilter.creator = { id: { eq: filter.creatorId } };
    if (filter.state) linearFilter.state = { type: { eq: filter.state } };
    if (filter.priority !== undefined) linearFilter.priority = { eq: filter.priority };
    if (filter.search) linearFilter.title = { containsIgnoreCase: filter.search };
    if (filter.hasProject !== undefined) {
      linearFilter.project = filter.hasProject ? { null: false } : { null: true };
    }
    if (filter.hasAssignee !== undefined) {
      linearFilter.assignee = filter.hasAssignee ? { null: false } : { null: true };
    }

    return linearFilter;
  }

  private buildProjectFilter(filter?: ProjectFilter): any {
    if (!filter) return undefined;

    const linearFilter: any = {};

    if (filter.teamIds?.length) {
      linearFilter.teams = { some: { id: { in: filter.teamIds } } };
    }
    if (filter.leadId) linearFilter.lead = { id: { eq: filter.leadId } };
    if (filter.state) linearFilter.state = { eq: filter.state };
    if (filter.search) linearFilter.name = { containsIgnoreCase: filter.search };

    return linearFilter;
  }
}

// Export singleton instance
export const linearService = new LinearService();
