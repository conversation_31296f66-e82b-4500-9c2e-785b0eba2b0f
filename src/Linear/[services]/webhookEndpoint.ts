/**
 * Linear Webhook Endpoint Handler
 * 
 * Utility for handling Linear webhook endpoints in a Firebase Functions context
 * This would typically be deployed as a serverless function
 */

import { LinearWebhookPayload } from '../[types]/Linear';
import { webhookService } from './webhookService';

/**
 * Webhook endpoint configuration
 */
export interface WebhookConfig {
  secret?: string;
  validateSignature?: boolean;
  allowedOrigins?: string[];
}

/**
 * Webhook endpoint handler
 */
export class LinearWebhookEndpoint {
  private config: WebhookConfig;

  constructor(config: WebhookConfig = {}) {
    this.config = {
      validateSignature: false,
      allowedOrigins: [],
      ...config
    };
  }

  /**
   * Handle incoming webhook request
   * This would be used in a Firebase Function or similar serverless environment
   */
  async handleWebhookRequest(
    body: string,
    headers: Record<string, string>
  ): Promise<{ status: number; message: string }> {
    try {
      // Validate request origin if configured
      if (this.config.allowedOrigins && this.config.allowedOrigins.length > 0) {
        const origin = headers.origin || headers.referer;
        if (origin && !this.config.allowedOrigins.some(allowed => origin.includes(allowed))) {
          return { status: 403, message: 'Forbidden: Invalid origin' };
        }
      }

      // Validate webhook signature if configured
      if (this.config.validateSignature && this.config.secret) {
        const signature = headers['x-linear-signature'] || headers['linear-signature'];
        if (!signature) {
          return { status: 401, message: 'Unauthorized: Missing signature' };
        }

        const isValid = webhookService.validateWebhookSignature(body, signature, this.config.secret);
        if (!isValid) {
          return { status: 401, message: 'Unauthorized: Invalid signature' };
        }
      }

      // Parse webhook payload
      let payload: LinearWebhookPayload;
      try {
        payload = JSON.parse(body);
      } catch (error) {
        return { status: 400, message: 'Bad Request: Invalid JSON' };
      }

      // Validate payload structure
      if (!this.isValidWebhookPayload(payload)) {
        return { status: 400, message: 'Bad Request: Invalid payload structure' };
      }

      // Process webhook
      await webhookService.processWebhook(payload);

      return { status: 200, message: 'Webhook processed successfully' };
    } catch (error) {
      console.error('Error handling webhook request:', error);
      return { status: 500, message: 'Internal Server Error' };
    }
  }

  /**
   * Validate webhook payload structure
   */
  private isValidWebhookPayload(payload: any): payload is LinearWebhookPayload {
    return (
      payload &&
      typeof payload.action === 'string' &&
      typeof payload.type === 'string' &&
      payload.data &&
      typeof payload.organizationId === 'string' &&
      typeof payload.webhookTimestamp === 'number' &&
      typeof payload.webhookId === 'string'
    );
  }
}

/**
 * Firebase Functions webhook handler
 * Example implementation for Firebase Functions
 */
export const createFirebaseWebhookHandler = (config: WebhookConfig = {}) => {
  const endpoint = new LinearWebhookEndpoint(config);

  return async (req: any, res: any) => {
    // Handle CORS preflight
    if (req.method === 'OPTIONS') {
      res.set('Access-Control-Allow-Origin', '*');
      res.set('Access-Control-Allow-Methods', 'POST');
      res.set('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-Linear-Signature');
      res.status(204).send('');
      return;
    }

    // Only allow POST requests
    if (req.method !== 'POST') {
      res.status(405).send('Method Not Allowed');
      return;
    }

    try {
      const body = typeof req.body === 'string' ? req.body : JSON.stringify(req.body);
      const headers = req.headers || {};

      const result = await endpoint.handleWebhookRequest(body, headers);
      
      res.status(result.status).send(result.message);
    } catch (error) {
      console.error('Firebase webhook handler error:', error);
      res.status(500).send('Internal Server Error');
    }
  };
};

/**
 * Express.js webhook handler
 * Example implementation for Express.js
 */
export const createExpressWebhookHandler = (config: WebhookConfig = {}) => {
  const endpoint = new LinearWebhookEndpoint(config);

  return async (req: any, res: any) => {
    try {
      const body = req.rawBody || JSON.stringify(req.body);
      const headers = req.headers || {};

      const result = await endpoint.handleWebhookRequest(body, headers);
      
      res.status(result.status).json({ message: result.message });
    } catch (error) {
      console.error('Express webhook handler error:', error);
      res.status(500).json({ message: 'Internal Server Error' });
    }
  };
};

/**
 * Webhook URL generator
 */
export const generateWebhookUrl = (baseUrl: string, endpoint: string = '/webhooks/linear'): string => {
  return `${baseUrl.replace(/\/$/, '')}${endpoint}`;
};

/**
 * Webhook setup instructions
 */
export const getWebhookSetupInstructions = (webhookUrl: string): string => {
  return `
To set up Linear webhooks:

1. Go to your Linear workspace settings
2. Navigate to API > Webhooks
3. Click "Create webhook"
4. Set the URL to: ${webhookUrl}
5. Select the events you want to receive:
   - Issue created
   - Issue updated
   - Issue deleted
   - Project created
   - Project updated
   - Project deleted
   - Comment created
   - Comment updated
   - Comment deleted
6. Save the webhook

The webhook will now send events to your TRQ application for real-time synchronization.
  `.trim();
};

// Export default endpoint instance
export const defaultWebhookEndpoint = new LinearWebhookEndpoint();
