# Linear Integration Module

This module provides comprehensive integration with Linear for project tracking and issue management within the TRQ application.

## Features

- **Linear Authentication**: Secure API key-based authentication with Linear
- **Project Management**: View, create, edit, and delete Linear projects
- **Issue Tracking**: Comprehensive issue management with filtering and search
- **Real-time Dashboard**: Analytics and metrics for Linear projects and issues
- **Webhook Integration**: Real-time synchronization with Linear changes
- **Team Management**: Multi-team support with team selection
- **Settings Management**: Configurable integration settings

## Components

### Pages
- `LinearDashboard`: Main dashboard with project and issue analytics
- `ProjectsPage`: Project management interface
- `IssuesPage`: Issue tracking and management
- `LinearSettings`: Configuration and settings page

### Components
- `ProjectList`: Display and manage Linear projects
- `ProjectForm`: Create and edit project forms
- `ProjectDetails`: Detailed project view
- `IssueList`: Display and manage Linear issues
- `IssueForm`: Create and edit issue forms
- `WebhookSettings`: Webhook configuration component

### Services
- `linearService`: Main service for Linear API interactions
- `webhookService`: Webhook event processing
- `webhookEndpoint`: Webhook endpoint handlers

### Context
- `LinearContext`: React context for Linear state management

## Setup

### 1. Install Dependencies

The Linear SDK is already installed as part of the project dependencies.

### 2. Configure Environment Variables

Add your Linear API key to the `.env` file:

```env
VITE_LINEAR_API_KEY=your_linear_api_key_here
VITE_LINEAR_WEBHOOK_SECRET=your_webhook_secret_here
```

### 3. Add Linear Provider

Wrap your application with the Linear provider:

```tsx
import { LinearProvider } from 'Linear';

function App() {
  return (
    <LinearProvider>
      {/* Your app components */}
    </LinearProvider>
  );
}
```

### 4. Configure Navigation

The Linear menu items are already configured in `Navigation/MenuItems.ts`. Add them to your navigation structure based on user roles.

### 5. Set up Routes

Import and add Linear routes to your routing configuration:

```tsx
import LinearRoutes from 'Linear/routes';

// Add to your route configuration
const routes = [
  ...LinearRoutes,
  // other routes
];
```

## Usage

### Basic Authentication

```tsx
import { useLinear } from 'Linear';

function MyComponent() {
  const { initialize, isAuthenticated, currentUser } = useLinear();

  const handleLogin = async () => {
    await initialize('your-api-key');
  };

  return (
    <div>
      {isAuthenticated ? (
        <p>Welcome, {currentUser?.displayName}!</p>
      ) : (
        <button onClick={handleLogin}>Connect to Linear</button>
      )}
    </div>
  );
}
```

### Using Linear Services

```tsx
import { linearService } from 'Linear';

// Get projects
const projects = await linearService.getProjects();

// Create an issue
const issue = await linearService.createIssue({
  title: 'New issue',
  teamId: 'team-id',
  description: 'Issue description'
});

// Get issues with filters
const issues = await linearService.getIssues({
  teamId: 'team-id',
  assigneeId: 'user-id'
});
```

### Webhook Integration

#### Frontend Setup

```tsx
import { WebhookSettings } from 'Linear';

function SettingsPage() {
  return (
    <WebhookSettings
      webhookUrl="https://your-domain.com/webhooks/linear"
      webhookEnabled={true}
      onWebhookUrlChange={(url) => console.log('Webhook URL:', url)}
      onTestWebhook={async () => {
        // Test webhook logic
        return true;
      }}
    />
  );
}
```

#### Backend Setup (Firebase Functions)

```typescript
import { createFirebaseWebhookHandler } from 'Linear/services/webhookEndpoint';

export const linearWebhook = createFirebaseWebhookHandler({
  secret: process.env.LINEAR_WEBHOOK_SECRET,
  validateSignature: true
});
```

## API Reference

### LinearService

#### Authentication
- `initialize(config: LinearConfig): Promise<void>`
- `getCurrentUser(): Promise<LinearUser>`

#### Projects
- `getProjects(filter?: ProjectFilter): Promise<LinearProject[]>`
- `getProject(projectId: string): Promise<LinearProject | null>`
- `createProject(input: CreateProjectInput): Promise<LinearProject>`
- `updateProject(input: UpdateProjectInput): Promise<LinearProject>`
- `deleteProject(projectId: string): Promise<boolean>`

#### Issues
- `getIssues(filter?: IssueFilter): Promise<LinearIssue[]>`
- `getIssue(issueId: string): Promise<LinearIssue | null>`
- `createIssue(input: CreateIssueInput): Promise<LinearIssue>`
- `updateIssue(input: UpdateIssueInput): Promise<LinearIssue>`
- `deleteIssue(issueId: string): Promise<boolean>`

#### Teams and Users
- `getTeams(): Promise<LinearTeam[]>`
- `getUsers(): Promise<LinearUser[]>`
- `getWorkflowStates(teamId: string): Promise<LinearWorkflowState[]>`
- `getLabels(teamId?: string): Promise<LinearIssueLabel[]>`

### LinearContext

#### State
- `isInitialized: boolean`
- `isAuthenticated: boolean`
- `isLoading: boolean`
- `currentUser: LinearUser | null`
- `teams: LinearTeam[]`
- `selectedTeamId: string | null`
- `settings: LinearIntegrationSettings | null`
- `error: string | null`

#### Methods
- `initialize(apiKey: string): Promise<void>`
- `logout(): void`
- `selectTeam(teamId: string): void`
- `refreshTeams(): Promise<void>`
- `updateSettings(settings: Partial<LinearIntegrationSettings>): Promise<void>`

## Types

All TypeScript types are available in `Linear/types/Linear.ts`:

- `LinearProject`
- `LinearIssue`
- `LinearUser`
- `LinearTeam`
- `LinearWorkflowState`
- `LinearIssueLabel`
- `LinearComment`
- `CreateIssueInput`
- `UpdateIssueInput`
- `CreateProjectInput`
- `UpdateProjectInput`
- `IssueFilter`
- `ProjectFilter`
- `LinearIntegrationSettings`

## Error Handling

The Linear integration includes comprehensive error handling:

```tsx
import { useLinear } from 'Linear';

function MyComponent() {
  const { error, isLoading } = useLinear();

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error}</div>;

  // Component content
}
```

## Security Considerations

1. **API Keys**: Store Linear API keys securely and never expose them in client-side code
2. **Webhook Secrets**: Use webhook secrets to validate incoming webhook requests
3. **CORS**: Configure CORS properly for webhook endpoints
4. **Rate Limiting**: Implement rate limiting for API calls to avoid hitting Linear's limits

## Troubleshooting

### Common Issues

1. **Authentication Failed**: Check that your Linear API key is valid and has the necessary permissions
2. **Team Not Found**: Ensure the selected team ID exists and you have access to it
3. **Webhook Not Receiving Events**: Verify the webhook URL is publicly accessible and returns 200 status codes

### Debug Mode

Enable debug logging by setting the environment variable:

```env
VITE_LINEAR_DEBUG=true
```

## Contributing

When contributing to the Linear integration:

1. Follow the existing code structure and patterns
2. Add proper TypeScript types for new features
3. Include error handling for all API calls
4. Update this README for new features
5. Add tests for new functionality

## License

This Linear integration is part of the TRQ application and follows the same license terms.
