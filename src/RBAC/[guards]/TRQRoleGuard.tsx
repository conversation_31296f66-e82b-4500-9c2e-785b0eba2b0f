import { Navigate } from 'react-router-dom';
import { useAuth } from 'Authentication/[contexts]/AuthContext';
import { useRole } from 'RBAC/[contexts]/RoleContext';
import Loader from '[components]/Loader';
import ErrorFallback from '[components]/ErrorFallback';
import { Role } from 'RBAC/[types]/Role';
import { ReactNode } from 'react';
import React from 'react';
import ROUTES from 'Routing/appRoutes';

interface RoleGuardProps {
  requiredRoles: Role | Role[];
  children: ReactNode;
  fallbackPath?: string; // Optional: where to redirect if unauthorized
}

/**
 * Role guard: ensures authenticated user has one of the required roles.
 * Usage: <TRQRoleGuard requiredRoles={Role.Admin}>...</TRQRoleGuard>
 *        <TRQRoleGuard requiredRoles={[Role.Doctor, Role.ClinicAdmin]}>...</TRQRoleGuard>
 */
export default function TRQRoleGuard({ requiredRoles, children, fallbackPath = ROUTES.AUTH.UNAUTHORIZED }: RoleGuardProps) {
  const { isAuthenticated, isLoading: isAuthLoading, error: authError } = useAuth();
  const { role, isRoleLoading } = useRole();
  const [timedOut, setTimedOut] = React.useState(false);

  React.useEffect(() => {
    if (!isAuthLoading && !isRoleLoading) return;
    const timer = setTimeout(() => setTimedOut(true), 10000); // 10s timeout
    return () => clearTimeout(timer);
  }, [isAuthLoading, isRoleLoading]);

  if (authError) return <ErrorFallback error={authError} />;
  if (timedOut) return (
    <ErrorFallback error={new Error('Authorization is taking too long. Please try again.')} resetErrorBoundary={() => window.location.reload()} />
  );
  if (isAuthLoading || isRoleLoading) return <Loader />;
  if (!isAuthenticated) return <Navigate to={ROUTES.AUTH.LOGIN} replace />;

  const allowedRoles = Array.isArray(requiredRoles) ? requiredRoles : [requiredRoles];
  if (!role || !allowedRoles.includes(role)) {
    console.warn(`Role-based access denied. Required: ${JSON.stringify(allowedRoles)}, Current: ${role}`);
    return <Navigate to={fallbackPath} replace />;
  }

  return <>{children}</>;
}
