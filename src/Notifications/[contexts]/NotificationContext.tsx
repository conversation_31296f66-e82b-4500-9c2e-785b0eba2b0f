import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { useAuth } from '../../Authentication/[contexts]/AuthContext';
import { notificationService } from '../[services]/notificationService';
import { Notification, NotificationStatus, CreateNotificationPayload, UserNotificationPreferences } from '../[types]/Notification';

interface NotificationContextType {
  notifications: Notification[];
  unreadCount: number;
  preferences: UserNotificationPreferences | null;
  loading: boolean;
  error: Error | null;
  getNotifications: (status?: NotificationStatus) => Promise<void>;
  getNotification: (id: string) => Promise<Notification | null>;
  markAsRead: (id: string) => Promise<void>;
  markAllAsRead: () => Promise<void>;
  archiveNotification: (id: string) => Promise<void>;
  deleteNotification: (id: string) => Promise<void>;
  createNotification: (payload: Omit<CreateNotificationPayload, 'userId'>) => Promise<string>;
  getUserPreferences: () => Promise<UserNotificationPreferences | null>;
  updateUserPreferences: (preferences: UserNotificationPreferences) => Promise<void>;
  createDefaultPreferences: () => Promise<void>;
}

const NotificationContext = createContext<NotificationContextType | undefined>(undefined);

export const useNotifications = (): NotificationContextType => {
  const context = useContext(NotificationContext);
  if (!context) {
    throw new Error('useNotifications must be used within a NotificationProvider');
  }
  return context;
};

interface NotificationProviderProps {
  children: ReactNode;
}

export const NotificationProvider: React.FC<NotificationProviderProps> = ({ children }) => {
  const { currentUser } = useAuth();
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [unreadCount, setUnreadCount] = useState<number>(0);
  const [preferences, setPreferences] = useState<UserNotificationPreferences | null>(null);
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<Error | null>(null);

  // Load notifications when user changes
  useEffect(() => {
    if (currentUser) {
      getNotifications();
      getUnreadCount();
      getUserPreferences();
    } else {
      // Clear notifications when user logs out
      setNotifications([]);
      setUnreadCount(0);
      setPreferences(null);
    }
  }, [currentUser]);

  // Get all notifications for the current user
  const getNotifications = async (status?: NotificationStatus): Promise<void> => {
    if (!currentUser) return;

    setLoading(true);
    setError(null);

    try {
      const fetchedNotifications = await notificationService.getUserNotifications(currentUser.uid, status);
      setNotifications(fetchedNotifications);
    } catch (err) {
      console.error('Error fetching notifications:', err);
      setError(err instanceof Error ? err : new Error('Failed to fetch notifications'));
    } finally {
      setLoading(false);
    }
  };

  // Get a single notification by ID
  const getNotification = async (id: string): Promise<Notification | null> => {
    if (!currentUser) return null;

    setLoading(true);
    setError(null);

    try {
      return await notificationService.getNotification(id);
    } catch (err) {
      console.error('Error fetching notification:', err);
      setError(err instanceof Error ? err : new Error('Failed to fetch notification'));
      return null;
    } finally {
      setLoading(false);
    }
  };

  // Get unread notification count
  const getUnreadCount = async (): Promise<void> => {
    if (!currentUser) return;

    try {
      const count = await notificationService.getUnreadCount(currentUser.uid);
      setUnreadCount(count);
    } catch (err) {
      console.error('Error fetching unread count:', err);
    }
  };

  // Mark a notification as read
  const markAsRead = async (id: string): Promise<void> => {
    if (!currentUser) return;

    setLoading(true);
    setError(null);

    try {
      await notificationService.markAsRead(id);

      // Update local state
      setNotifications((prev) =>
        prev.map((notification) => (notification.id === id ? { ...notification, status: NotificationStatus.READ } : notification))
      );

      // Update unread count
      getUnreadCount();
    } catch (err) {
      console.error('Error marking notification as read:', err);
      setError(err instanceof Error ? err : new Error('Failed to mark notification as read'));
    } finally {
      setLoading(false);
    }
  };

  // Mark all notifications as read
  const markAllAsRead = async (): Promise<void> => {
    if (!currentUser) return;

    setLoading(true);
    setError(null);

    try {
      await notificationService.markAllAsRead(currentUser.uid);

      // Update local state
      setNotifications((prev) =>
        prev.map((notification) =>
          notification.status === NotificationStatus.UNREAD ? { ...notification, status: NotificationStatus.READ } : notification
        )
      );

      // Update unread count
      setUnreadCount(0);
    } catch (err) {
      console.error('Error marking all notifications as read:', err);
      setError(err instanceof Error ? err : new Error('Failed to mark all notifications as read'));
    } finally {
      setLoading(false);
    }
  };

  // Archive a notification
  const archiveNotification = async (id: string): Promise<void> => {
    if (!currentUser) return;

    setLoading(true);
    setError(null);

    try {
      await notificationService.archiveNotification(id);

      // Update local state
      setNotifications((prev) =>
        prev.map((notification) => (notification.id === id ? { ...notification, status: NotificationStatus.ARCHIVED } : notification))
      );

      // Update unread count if needed
      getUnreadCount();
    } catch (err) {
      console.error('Error archiving notification:', err);
      setError(err instanceof Error ? err : new Error('Failed to archive notification'));
    } finally {
      setLoading(false);
    }
  };

  // Delete a notification
  const deleteNotification = async (id: string): Promise<void> => {
    if (!currentUser) return;

    setLoading(true);
    setError(null);

    try {
      await notificationService.deleteNotification(id);

      // Update local state
      setNotifications((prev) => prev.filter((notification) => notification.id !== id));

      // Update unread count if needed
      getUnreadCount();
    } catch (err) {
      console.error('Error deleting notification:', err);
      setError(err instanceof Error ? err : new Error('Failed to delete notification'));
    } finally {
      setLoading(false);
    }
  };

  // Create a new notification
  const createNotification = async (payload: Omit<CreateNotificationPayload, 'userId'>): Promise<string> => {
    if (!currentUser) throw new Error('User must be logged in to create notifications');

    setLoading(true);
    setError(null);

    try {
      const fullPayload: CreateNotificationPayload = {
        ...payload,
        userId: currentUser.uid
      };

      const id = await notificationService.createNotification(fullPayload);

      // Refresh notifications and unread count
      getNotifications();
      getUnreadCount();

      return id;
    } catch (err) {
      console.error('Error creating notification:', err);
      setError(err instanceof Error ? err : new Error('Failed to create notification'));
      throw err;
    } finally {
      setLoading(false);
    }
  };

  // Get user notification preferences
  const getUserPreferences = async (): Promise<UserNotificationPreferences | null> => {
    if (!currentUser) return null;

    setLoading(true);
    setError(null);

    try {
      const prefs = await notificationService.getUserPreferences(currentUser.uid);
      setPreferences(prefs);
      return prefs;
    } catch (err) {
      console.error('Error fetching user preferences:', err);
      setError(err instanceof Error ? err : new Error('Failed to fetch user preferences'));
      return null;
    } finally {
      setLoading(false);
    }
  };

  // Update user notification preferences
  const updateUserPreferences = async (prefs: UserNotificationPreferences): Promise<void> => {
    if (!currentUser) return;

    setLoading(true);
    setError(null);

    try {
      await notificationService.updateUserPreferences(prefs);
      setPreferences(prefs);
    } catch (err) {
      console.error('Error updating user preferences:', err);
      setError(err instanceof Error ? err : new Error('Failed to update user preferences'));
    } finally {
      setLoading(false);
    }
  };

  // Create default notification preferences
  const createDefaultPreferences = async (): Promise<void> => {
    if (!currentUser) return;

    setLoading(true);
    setError(null);

    try {
      await notificationService.createDefaultPreferences(currentUser.uid);
      await getUserPreferences();
    } catch (err) {
      console.error('Error creating default preferences:', err);
      setError(err instanceof Error ? err : new Error('Failed to create default preferences'));
    } finally {
      setLoading(false);
    }
  };

  const value: NotificationContextType = {
    notifications,
    unreadCount,
    preferences,
    loading,
    error,
    getNotifications,
    getNotification,
    markAsRead,
    markAllAsRead,
    archiveNotification,
    deleteNotification,
    createNotification,
    getUserPreferences,
    updateUserPreferences,
    createDefaultPreferences
  };

  return <NotificationContext.Provider value={value}>{children}</NotificationContext.Provider>;
};
