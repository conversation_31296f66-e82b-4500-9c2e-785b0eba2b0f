import React, { useState, useRef, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useNotifications } from '../[contexts]/NotificationContext';
import { NotificationStatus } from '../[types]/Notification';
import { Badge, IconButton, Menu, MenuItem, Typography, Box, Divider, Button, Tooltip } from '@mui/material';
import NotificationsIcon from '@mui/icons-material/Notifications';
import NotificationsNoneIcon from '@mui/icons-material/NotificationsNone';
import CheckCircleOutlineIcon from '@mui/icons-material/CheckCircleOutline';
import DeleteOutlineIcon from '@mui/icons-material/DeleteOutline';
import { formatDistanceToNow } from 'date-fns';
import ROUTES from '../../Routing/appRoutes';

/**
 * NotificationBell component displays a bell icon with a badge showing the number of unread notifications
 * and a dropdown menu with the list of notifications.
 */
const NotificationBell: React.FC = () => {
  const { notifications, unreadCount, loading, markAsRead, markAllAsRead, deleteNotification, getNotifications } = useNotifications();
  const navigate = useNavigate();

  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const open = Boolean(anchorEl);
  const menuRef = useRef<HTMLDivElement>(null);

  // Fetch notifications when the component mounts
  useEffect(() => {
    getNotifications();
  }, [getNotifications]);

  // Handle click on the bell icon
  const handleClick = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };

  // Handle close of the dropdown menu
  const handleClose = () => {
    setAnchorEl(null);
  };

  // Handle marking a notification as read
  const handleMarkAsRead = async (id: string, event: React.MouseEvent) => {
    event.stopPropagation();
    await markAsRead(id);
  };

  // Handle marking all notifications as read
  const handleMarkAllAsRead = async () => {
    await markAllAsRead();
  };

  // Handle deleting a notification
  const handleDelete = async (id: string, event: React.MouseEvent) => {
    event.stopPropagation();
    await deleteNotification(id);
  };

  // Handle clicking on a notification
  const handleNotificationClick = (notification: any) => {
    // Mark as read
    if (notification.status === NotificationStatus.UNREAD) {
      markAsRead(notification.id);
    }

    // Navigate to the notification link if available
    if (notification.linkUrl) {
      if (notification.linkUrl.startsWith('http')) {
        window.open(notification.linkUrl, '_blank');
      } else {
        navigate(notification.linkUrl);
      }
    }

    handleClose();
  };

  // Format the notification timestamp
  const formatTimestamp = (timestamp: any) => {
    try {
      if (!timestamp) return '';

      // Convert Firebase Timestamp to Date
      const date = timestamp.toDate ? timestamp.toDate() : new Date(timestamp);
      return formatDistanceToNow(date, { addSuffix: true });
    } catch (error) {
      console.error('Error formatting timestamp:', error);
      return '';
    }
  };

  return (
    <>
      <Tooltip title="Notifications">
        <IconButton color="inherit" onClick={handleClick} size="large" aria-label={`${unreadCount} unread notifications`}>
          <Badge badgeContent={unreadCount} color="error">
            {unreadCount > 0 ? <NotificationsIcon /> : <NotificationsNoneIcon />}
          </Badge>
        </IconButton>
      </Tooltip>

      <Menu
        anchorEl={anchorEl}
        open={open}
        onClose={handleClose}
        PaperProps={{
          style: {
            maxHeight: 'calc(100% - 96px)',
            width: '350px',
            maxWidth: '100%',
            overflow: 'auto'
          },
          ref: menuRef
        }}
        transformOrigin={{ horizontal: 'right', vertical: 'top' }}
        anchorOrigin={{ horizontal: 'right', vertical: 'bottom' }}
      >
        <Box sx={{ p: 2, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Typography variant="h6">Notifications</Typography>
          {unreadCount > 0 && (
            <Button size="small" onClick={handleMarkAllAsRead} startIcon={<CheckCircleOutlineIcon />}>
              Mark all as read
            </Button>
          )}
        </Box>

        <Divider />

        {loading ? (
          <Box sx={{ p: 2, textAlign: 'center' }}>
            <Typography>Loading notifications...</Typography>
          </Box>
        ) : notifications.length === 0 ? (
          <Box sx={{ p: 2, textAlign: 'center' }}>
            <Typography>No notifications</Typography>
          </Box>
        ) : (
          notifications.map((notification) => (
            <MenuItem
              key={notification.id}
              onClick={() => handleNotificationClick(notification)}
              sx={{
                py: 1.5,
                px: 2,
                borderLeft: notification.status === NotificationStatus.UNREAD ? '4px solid #1976d2' : 'none',
                backgroundColor: notification.status === NotificationStatus.UNREAD ? 'rgba(25, 118, 210, 0.08)' : 'inherit',
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'flex-start',
                gap: 0.5
              }}
            >
              <Box sx={{ width: '100%', display: 'flex', justifyContent: 'space-between' }}>
                <Typography variant="subtitle2" sx={{ fontWeight: 'bold' }}>
                  {notification.title}
                </Typography>
                <Typography variant="caption" color="text.secondary">
                  {formatTimestamp(notification.createdAt)}
                </Typography>
              </Box>

              <Typography variant="body2" sx={{ color: 'text.primary', width: '100%' }}>
                {notification.message}
              </Typography>

              <Box
                sx={{
                  mt: 1,
                  width: '100%',
                  display: 'flex',
                  justifyContent: 'flex-end',
                  gap: 1
                }}
              >
                {notification.status === NotificationStatus.UNREAD && (
                  <Tooltip title="Mark as read">
                    <IconButton size="small" onClick={(e) => handleMarkAsRead(notification.id, e)}>
                      <CheckCircleOutlineIcon fontSize="small" />
                    </IconButton>
                  </Tooltip>
                )}
                <Tooltip title="Delete">
                  <IconButton size="small" onClick={(e) => handleDelete(notification.id, e)}>
                    <DeleteOutlineIcon fontSize="small" />
                  </IconButton>
                </Tooltip>
              </Box>
            </MenuItem>
          ))
        )}

        {notifications.length > 0 && (
          <>
            <Divider />
            <Box sx={{ p: 1, display: 'flex', justifyContent: 'center' }}>
              <Button
                size="small"
                onClick={() => {
                  handleClose();
                  // Navigate to notification center
                  navigate(ROUTES.NOTIFICATIONS.CENTER);
                }}
              >
                View All
              </Button>
            </Box>
          </>
        )}
      </Menu>
    </>
  );
};

export default NotificationBell;
