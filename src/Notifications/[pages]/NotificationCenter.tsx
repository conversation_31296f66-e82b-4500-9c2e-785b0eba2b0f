import React, { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useNotifications } from '../[contexts]/NotificationContext';
import { NotificationStatus, NotificationType } from '../[types]/Notification';
import {
  Container,
  Typography,
  Box,
  Tabs,
  Tab,
  Paper,
  List,
  ListItem,
  ListItemText,
  IconButton,
  Divider,
  Button,
  Chip,
  CircularProgress,
  Alert,
  Menu,
  MenuItem,
  Tooltip
} from '@mui/material';
import CheckCircleOutlineIcon from '@mui/icons-material/CheckCircleOutline';
import MoreVertIcon from '@mui/icons-material/MoreVert';
import FilterListIcon from '@mui/icons-material/FilterList';
import { formatDistanceToNow } from 'date-fns';
import MainCard from '../../[components]/cards/MainCard';

// Map notification types to colors
const typeToColor: Record<NotificationType, string> = {
  [NotificationType.SYSTEM]: 'info',
  [NotificationType.PURCHASE]: 'success',
  [NotificationType.QUESTIONNAIRE]: 'primary',
  [NotificationType.APPOINTMENT]: 'warning',
  [NotificationType.MESSAGE]: 'secondary',
  [NotificationType.REMINDER]: 'warning',
  [NotificationType.ALERT]: 'error',
  [NotificationType.ANNOUNCEMENT]: 'info',
  [NotificationType.PAYMENT]: 'success',
  [NotificationType.OTHER]: 'default'
};

// Map notification types to readable labels
const typeToLabel: Record<NotificationType, string> = {
  [NotificationType.SYSTEM]: 'System',
  [NotificationType.PURCHASE]: 'Purchase',
  [NotificationType.QUESTIONNAIRE]: 'Questionnaire',
  [NotificationType.APPOINTMENT]: 'Appointment',
  [NotificationType.MESSAGE]: 'Message',
  [NotificationType.REMINDER]: 'Reminder',
  [NotificationType.ALERT]: 'Alert',
  [NotificationType.ANNOUNCEMENT]: 'Announcement',
  [NotificationType.PAYMENT]: 'Payment',
  [NotificationType.OTHER]: 'Other'
};

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

const TabPanel: React.FC<TabPanelProps> = (props) => {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`notification-tabpanel-${index}`}
      aria-labelledby={`notification-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
    </div>
  );
};

const NotificationCenter: React.FC = () => {
  const { notifications, loading, error, getNotifications, markAsRead, markAllAsRead, archiveNotification, deleteNotification } =
    useNotifications();
  const navigate = useNavigate();

  const [tabValue, setTabValue] = useState(0);
  const [filterAnchorEl, setFilterAnchorEl] = useState<null | HTMLElement>(null);
  const [selectedTypes, setSelectedTypes] = useState<NotificationType[]>([]);
  const [actionAnchorEl, setActionAnchorEl] = useState<{ [key: string]: HTMLElement | null }>({});

  // Fetch notifications when the component mounts
  useEffect(() => {
    getNotifications();
  }, [getNotifications]);

  // Handle tab change
  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);

    // Fetch notifications based on the selected tab
    if (newValue === 0) {
      getNotifications(); // All notifications
    } else if (newValue === 1) {
      getNotifications(NotificationStatus.UNREAD); // Unread notifications
    } else if (newValue === 2) {
      getNotifications(NotificationStatus.READ); // Read notifications
    } else if (newValue === 3) {
      getNotifications(NotificationStatus.ARCHIVED); // Archived notifications
    }
  };

  // Handle filter menu open
  const handleFilterClick = (event: React.MouseEvent<HTMLElement>) => {
    setFilterAnchorEl(event.currentTarget);
  };

  // Handle filter menu close
  const handleFilterClose = () => {
    setFilterAnchorEl(null);
  };

  // Handle filter selection
  const handleFilterSelect = (type: NotificationType) => {
    if (selectedTypes.includes(type)) {
      setSelectedTypes(selectedTypes.filter((t) => t !== type));
    } else {
      setSelectedTypes([...selectedTypes, type]);
    }
  };

  // Handle action menu open
  const handleActionClick = (event: React.MouseEvent<HTMLElement>, id: string) => {
    event.stopPropagation();
    setActionAnchorEl({ ...actionAnchorEl, [id]: event.currentTarget });
  };

  // Handle action menu close
  const handleActionClose = (id: string) => {
    setActionAnchorEl({ ...actionAnchorEl, [id]: null });
  };

  // Handle marking a notification as read
  const handleMarkAsRead = async (id: string, event?: React.MouseEvent) => {
    if (event) event.stopPropagation();
    await markAsRead(id);
    handleActionClose(id);
  };

  // Handle archiving a notification
  const handleArchive = async (id: string, event?: React.MouseEvent) => {
    if (event) event.stopPropagation();
    await archiveNotification(id);
    handleActionClose(id);
  };

  // Handle deleting a notification
  const handleDelete = async (id: string, event?: React.MouseEvent) => {
    if (event) event.stopPropagation();
    await deleteNotification(id);
    handleActionClose(id);
  };

  // Handle clicking on a notification
  const handleNotificationClick = (notification: any) => {
    // Mark as read if unread
    if (notification.status === NotificationStatus.UNREAD) {
      markAsRead(notification.id);
    }

    // Navigate to the notification link if available
    if (notification.linkUrl) {
      if (notification.linkUrl.startsWith('http')) {
        window.open(notification.linkUrl, '_blank');
      } else {
        navigate(notification.linkUrl);
      }
    }
  };

  // Format the notification timestamp
  const formatTimestamp = (timestamp: any) => {
    try {
      if (!timestamp) return '';

      // Convert Firebase Timestamp to Date
      const date = timestamp.toDate ? timestamp.toDate() : new Date(timestamp);
      return formatDistanceToNow(date, { addSuffix: true });
    } catch (error) {
      console.error('Error formatting timestamp:', error);
      return '';
    }
  };

  // Filter notifications based on selected types
  const filteredNotifications =
    selectedTypes.length > 0 ? notifications.filter((notification) => selectedTypes.includes(notification.type)) : notifications;

  // Count notifications by status
  const unreadCount = notifications.filter((n) => n.status === NotificationStatus.UNREAD).length;
  const readCount = notifications.filter((n) => n.status === NotificationStatus.READ).length;
  const archivedCount = notifications.filter((n) => n.status === NotificationStatus.ARCHIVED).length;

  return (
    <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
      <MainCard title="Notification Center">
        <Box sx={{ mb: 2, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Typography variant="h4" component="h1" gutterBottom>
            Notification Center
          </Typography>

          <Box>
            <Tooltip title="Filter by type">
              <IconButton onClick={handleFilterClick}>
                <FilterListIcon />
              </IconButton>
            </Tooltip>

            <Menu anchorEl={filterAnchorEl} open={Boolean(filterAnchorEl)} onClose={handleFilterClose}>
              {Object.values(NotificationType).map((type) => (
                <MenuItem key={type} onClick={() => handleFilterSelect(type)}>
                  <Chip
                    label={typeToLabel[type]}
                    color={typeToColor[type] as any}
                    size="small"
                    variant={selectedTypes.includes(type) ? 'filled' : 'outlined'}
                    sx={{ mr: 1 }}
                  />
                  {typeToLabel[type]}
                </MenuItem>
              ))}
            </Menu>

            {unreadCount > 0 && (
              <Button variant="outlined" size="small" startIcon={<CheckCircleOutlineIcon />} onClick={() => markAllAsRead()} sx={{ ml: 1 }}>
                Mark all as read
              </Button>
            )}
          </Box>
        </Box>

        {error && (
          <Alert severity="error" sx={{ mb: 2 }}>
            {error.message}
          </Alert>
        )}

        <Paper sx={{ width: '100%' }}>
          <Tabs value={tabValue} onChange={handleTabChange} indicatorColor="primary" textColor="primary" variant="fullWidth">
            <Tab label={`All (${notifications.length})`} />
            <Tab label={`Unread (${unreadCount})`} />
            <Tab label={`Read (${readCount})`} />
            <Tab label={`Archived (${archivedCount})`} />
          </Tabs>

          <TabPanel value={tabValue} index={0}>
            {renderNotificationList(filteredNotifications)}
          </TabPanel>

          <TabPanel value={tabValue} index={1}>
            {renderNotificationList(filteredNotifications.filter((n) => n.status === NotificationStatus.UNREAD))}
          </TabPanel>

          <TabPanel value={tabValue} index={2}>
            {renderNotificationList(filteredNotifications.filter((n) => n.status === NotificationStatus.READ))}
          </TabPanel>

          <TabPanel value={tabValue} index={3}>
            {renderNotificationList(filteredNotifications.filter((n) => n.status === NotificationStatus.ARCHIVED))}
          </TabPanel>
        </Paper>
      </MainCard>
    </Container>
  );

  // Helper function to render the notification list
  function renderNotificationList(notificationList: any[]) {
    if (loading) {
      return (
        <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
          <CircularProgress />
        </Box>
      );
    }

    if (notificationList.length === 0) {
      return (
        <Box sx={{ p: 3, textAlign: 'center' }}>
          <Typography variant="body1">No notifications found</Typography>
        </Box>
      );
    }

    return (
      <List>
        {notificationList.map((notification, index) => (
          <React.Fragment key={notification.id}>
            <ListItem
              alignItems="flex-start"
              sx={{
                cursor: 'pointer',
                backgroundColor: notification.status === NotificationStatus.UNREAD ? 'rgba(25, 118, 210, 0.08)' : 'inherit',
                '&:hover': {
                  backgroundColor: 'rgba(0, 0, 0, 0.04)'
                }
              }}
              onClick={() => handleNotificationClick(notification)}
            >
              <Box sx={{ width: '100%' }}>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                    <Typography variant="subtitle1" sx={{ fontWeight: 'bold' }}>
                      {notification.title}
                    </Typography>
                    <Chip
                      label={typeToLabel[notification.type as NotificationType] || 'Unknown'}
                      color={(typeToColor[notification.type as NotificationType] || 'default') as any}
                      size="small"
                      sx={{ ml: 1 }}
                    />
                    {notification.status === NotificationStatus.UNREAD && (
                      <Box
                        sx={{
                          width: 8,
                          height: 8,
                          borderRadius: '50%',
                          bgcolor: 'primary.main',
                          ml: 1
                        }}
                      />
                    )}
                  </Box>

                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    <Typography variant="caption" color="text.secondary">
                      {formatTimestamp(notification.createdAt)}
                    </Typography>

                    <IconButton
                      size="small"
                      onClick={(e) => handleActionClick(e, notification.id)}
                      aria-label="more"
                      aria-controls={`notification-menu-${notification.id}`}
                      aria-haspopup="true"
                    >
                      <MoreVertIcon />
                    </IconButton>

                    <Menu
                      id={`notification-menu-${notification.id}`}
                      anchorEl={actionAnchorEl[notification.id]}
                      open={Boolean(actionAnchorEl[notification.id])}
                      onClose={() => handleActionClose(notification.id)}
                    >
                      {notification.status === NotificationStatus.UNREAD && (
                        <MenuItem onClick={(e) => handleMarkAsRead(notification.id, e)}>
                          <ListItemText primary="Mark as read" />
                        </MenuItem>
                      )}
                      {notification.status !== NotificationStatus.ARCHIVED && (
                        <MenuItem onClick={(e) => handleArchive(notification.id, e)}>
                          <ListItemText primary="Archive" />
                        </MenuItem>
                      )}
                      <MenuItem onClick={(e) => handleDelete(notification.id, e)}>
                        <ListItemText primary="Delete" />
                      </MenuItem>
                    </Menu>
                  </Box>
                </Box>

                <Typography variant="body1" sx={{ mb: 1 }}>
                  {notification.message}
                </Typography>

                {notification.actions && notification.actions.length > 0 && (
                  <Box sx={{ mt: 1 }}>
                    {notification.actions.map((action: any, actionIndex: number) => (
                      <Button
                        key={actionIndex}
                        variant="outlined"
                        size="small"
                        href={action.url}
                        sx={{ mr: 1, mb: 1 }}
                        onClick={(e) => e.stopPropagation()}
                      >
                        {action.label}
                      </Button>
                    ))}
                  </Box>
                )}
              </Box>
            </ListItem>
            {index < notificationList.length - 1 && <Divider component="li" />}
          </React.Fragment>
        ))}
      </List>
    );
  }
};

export default NotificationCenter;
