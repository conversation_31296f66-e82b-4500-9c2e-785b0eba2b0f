/**
 * Notification types and interfaces for the Notifications module
 */

// Notification status enum
export enum NotificationStatus {
  UNREAD = 'unread',
  READ = 'read',
  ARCHIVED = 'archived'
}

// Notification type enum
export enum NotificationType {
  SYSTEM = 'system',
  PURCHASE = 'purchase',
  QUESTIONNAIRE = 'questionnaire',
  APPOINTMENT = 'appointment',
  MESSAGE = 'message',
  REMINDER = 'reminder',
  ALERT = 'alert',
  ANNOUNCEMENT = 'announcement',
  PAYMENT = 'payment',
  OTHER = 'other'
}

// Notification priority enum
export enum NotificationPriority {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high'
}

// Notification channel enum
export enum NotificationChannel {
  IN_APP = 'in_app',
  EMAIL = 'email',
  SMS = 'sms',
  PUSH = 'push'
}

// Notification action interface
export interface NotificationAction {
  label: string;
  url: string;
  type?: 'primary' | 'secondary' | 'error' | 'warning' | 'info' | 'success';
}

// Notification interface
export interface Notification {
  id: string;
  userId: string;
  title: string;
  message: string;
  type: NotificationType;
  status: NotificationStatus;
  priority?: NotificationPriority;
  createdAt: any; // Firebase Timestamp or Date
  updatedAt: any; // Firebase Timestamp or Date
  readAt?: any; // Firebase Timestamp or Date
  expiresAt?: any; // Firebase Timestamp or Date
  linkUrl?: string;
  imageUrl?: string;
  actions?: NotificationAction[];
  metadata?: Record<string, any>;
  channels?: NotificationChannel[];
  sourceId?: string;
  sourceType?: string;
}

// Create notification payload interface
export interface CreateNotificationPayload {
  userId: string;
  title: string;
  message: string;
  type: NotificationType;
  priority?: NotificationPriority;
  expiresAt?: Date;
  linkUrl?: string;
  imageUrl?: string;
  actions?: NotificationAction[];
  metadata?: Record<string, any>;
  channels?: NotificationChannel[];
  sourceId?: string;
  sourceType?: string;
  updatedAt?: any; // Firebase Timestamp or Date
}

// User notification preferences interface
export interface UserNotificationPreferences {
  userId: string;
  channels: {
    [NotificationChannel.IN_APP]: boolean;
    [NotificationChannel.EMAIL]: boolean;
    [NotificationChannel.SMS]: boolean;
    [NotificationChannel.PUSH]: boolean;
  };
  types: {
    [key in NotificationType]: {
      enabled: boolean;
      channels: NotificationChannel[];
    };
  };
  doNotDisturb?: {
    enabled: boolean;
    startTime?: string; // Format: "HH:MM"
    endTime?: string; // Format: "HH:MM"
    timezone?: string; // e.g., "America/New_York"
  };
  updatedAt: any; // Firebase Timestamp or Date
}
