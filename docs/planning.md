# Planning: Doctor's Office Client & Admin Portal

This document outlines the plan for building a Client and Admin portal for a doctor's office using the Berry React MUI template and Firebase.

## 1. Project Requirements

### 1.1. Core Functionality
-   **Client Portal**: Patients can log in, view assigned respiratory questionnaires, fill them out, and submit them.
-   **Admin Portal**: Staff can log in, manage patient accounts, manage questionnaires (create, update, assign), and view submitted questionnaire responses.

### 1.2. Technology Stack
-   **Frontend**: React (using Berry MUI Template)
-   **Backend/Database**: Firebase (Authentication, Firestore/Realtime Database)
-   **UI Library**: Material UI (MUI)

## 2. Firebase Setup & Integration

-   [ ] Set up Firebase project (if not already existing).
-   [ ] Configure Firebase Authentication (Email/Password, potentially other providers?).
-   [ ] Design Firestore/Realtime Database schema:
    -   `user_profiles` collection (Based on `UserProfile` model: Stores role, clinicId, etc. linked to Firebase Auth UID).
    -   `patients` collection (Based on `Patient` model: Stores patient demographic and medical info. Can be linked to `user_profiles` via `associatedUserId`).
    -   `questionnaires` collection (Based on `Questionnaire` model: Stores questionnaire templates, includes embedded `Question` objects).
    -   `assignments` collection (Tracks which questionnaires are assigned to which patients/users: `patientId`/`userId`, `questionnaireId`, `assignedDate`, `status` ['assigned', 'notified', 'started', 'completed']).
    -   `questionnaire_responses` collection (Based on `QuestionnaireResponse` model: Stores user's answers linked to `questionnaireId` and `userId`).
-   [ ] Implement Firebase SDK integration in the React app.
-   [ ] Create utility functions for interacting with Firebase services.

## 3. Template Customization (Berry React MUI)

-   [ ] **Strategy**: Preserve original template pages/routes. Build client/admin portals using prefixed routes (e.g., `/client`, `/admin`) and new components, leveraging template UI elements where applicable.
-   [ ] Identify relevant Berry layouts/pages for Admin dashboard (e.g., `DashboardDefault`, table pages).
-   [ ] Identify relevant Berry layouts/pages for Client portal (simpler layout, potentially login/register pages).
-   [ ] Set up React Router for navigation between public pages, client-authenticated pages, and admin-authenticated pages.
-   [ ] Customize theme (colors, typography) if needed to match branding.

## 4. Client Portal Development

-   [ ] Implement Authentication flow (Login, Registration, Password Reset).
-   [ ] Create Client Dashboard:
    -   Display assigned questionnaires (list).
    -   Show status (pending, completed).
-   [ ] Implement Questionnaire View/Fill component:
    -   Fetch questionnaire structure from Firebase.
    -   Render questions dynamically.
    -   Handle different question types (multiple choice, text input, etc.).
    -   Save progress (optional).
    -   Submit responses to Firebase.
-   [ ] (Optional) View past submissions/results.

## 5. Admin Portal Development

-   [ ] Implement Admin Authentication flow (separate or role-based).
-   [ ] Create Admin Dashboard:
    -   Overview stats (e.g., total patients, pending questionnaires).
-   [ ] Patient Management Module:
    -   List patients.
    -   Add/invite new patients.
    -   View patient details.
    -   Assign questionnaires to patients.
-   [ ] Questionnaire Management Module:
    -   List existing questionnaires.
    -   Create new questionnaires (define questions, types, options).
    -   Edit existing questionnaires.
    -   Delete questionnaires.
-   [ ] Response Viewing Module:
    -   View submitted responses per patient/questionnaire.
    -   Filter/search responses.

## 6. Questionnaire Logic

-   [ ] Define data structure for storing questionnaire questions and types in Firebase.
-   [ ] Implement logic for rendering different question types in the UI.
-   [ ] Implement validation for answers.
-   [ ] Define data structure for storing responses in Firebase.
-   [ ] (Optional) Implement scoring logic if required by the questionnaires.

## 7. Deployment

-   [ ] Configure build process for production.
-   [ ] Choose hosting platform (e.g., Firebase Hosting).
-   [ ] Set up CI/CD pipeline (optional).
-   [ ] Deploy the application.

## 8. Future Considerations / Enhancements

-   Notifications (email/in-app) for new assignments or submissions.
-   Reporting/Analytics for admin.
-   Accessibility improvements.
-   Integration with EMR/EHR systems.
