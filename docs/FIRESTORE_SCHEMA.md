# TRQ Firestore Schema Documentation

This document outlines the Firestore database schema for the TRQ application. It shows the structure of collections, documents, and their relationships.

## Type Definitions

### UserID

Throughout the application, we use the `UserID` type for any reference to a user identifier, regardless of the user's role (patient, doctor, admin, etc.). This is a type alias for the Firebase Auth UID.

```typescript
/**
 * Type alias for any user ID (patient, doctor, admin, etc.) which is based on Firebase Auth UID
 */
export type UserID = string;
```

All user ID fields in the database should be referenced and typed as `UserID` in the code to ensure consistency.

## Collections Overview

### Top-Level Collections

- `users` - User accounts 
- `clinics` - Medical clinics
- `patients` - Patient records
- `questionnaireTemplates` - Templates for questionnaires (formerly "tests")
- `questionnaires` - Instances of questionnaires completed by patients
- `complianceReports` - Medical compliance reports (also stored as subcollections under questionnaires)
- `purchases` - Purchase records for clients

### Role-Specific Collections (Denormalized from users)

- `doctors` - Doctor profiles
- `clinicAdmins` - Clinic administrators
- `assistants` - Clinic assistants
- `clients` - Client company profiles

## Document Schemas

### `users/{userId}`

User authentication and profile information.

```typescript
{
  email: string;
  displayName: string;
  role: 'admin' | 'clinic_admin' | 'doctor' | 'client' | 'patient';
  firstName: string;
  lastName: string;
  title?: string;  
  phone?: string;
  createdAt: timestamp;
  clinicId?: string; // For doctors/clinicAdmins
  metadata?: {
    securityStamp: string;
  }
}
```

### `clinics/{clinicId}`

Medical clinics information.

```typescript
{
  name: string;
  address: string;
  createdAt: timestamp;
}
```

### `doctors/{doctorId}`

Doctor profile information (denormalized from users).

```typescript
{
  userId: UserID; // Reference to users collection
  clinicId: string; // Reference to clinics collection
  firstName: string;
  lastName: string;
  email: string;
  phone?: string;
  title?: string;
  createdAt: timestamp;
}
```

### `clinicAdmins/{adminId}`

Clinic administrators (denormalized from users).

```typescript
{
  userId: UserID; // Reference to users collection
  clinicId: string; // Reference to clinics collection
  firstName: string;
  lastName: string;
  email: string;
  phone?: string;
  title?: string;
  createdAt: timestamp;
}
```

### `assistants/{assistantId}`

Clinic assistants (denormalized from users).

```typescript
{
  userId: UserID; // Reference to users collection
  clinicId: string; // Reference to clinics collection
  firstName: string;
  lastName: string;
  email: string;
  phone?: string;
  title?: string;
  createdAt: timestamp;
}
```

### `clients/{clientId}`

Client companies (denormalized from users).

```typescript
{
  userId: UserID; // Reference to users collection
  companyName: string;
  companyAddress?: string;
  firstName: string;
  lastName: string;
  email: string;
  phone?: string;
  title?: string;
  assignedDoctorId?: UserID; // Reference to doctors collection
  createdAt: timestamp;
}
```

### `patients/{patientId}`

Patient records.

```typescript
{
  firstName: string;
  lastName: string;
  email: string;
  phoneNumber?: string;
  gender: 'male' | 'female' | 'other';
  birthDate: timestamp;
  ssn?: string;
  height?: {
    feet: number;
    inches: number;
  };
  weight?: number;
  jobTitle?: string;
  bestTimeToCall?: string;
  clientId: UserID; // Reference to clients collection
  assignedDoctorId?: UserID; // Reference to doctors collection
  questionnaireIds?: string[]; // List of assigned questionnaire IDs (References questionnaires)
  createdAt: timestamp;
}
```

### `questionnaireTemplates/{templateId}`

Templates for questionnaires that can be assigned to patients.

```typescript
{
  name: string;
  description: string;
  price: number;
  createdAt: timestamp;
  createdBy: UserID; // Reference to users collection
  updatedAt?: timestamp;
  isPublished: boolean;
  clinicId?: string; // For clinic-specific templates, null if global
  isDefault: boolean; // For system default templates
  metadata?: {
    category?: string;
    estimatedTime?: number;
    tags?: string[];
  }
}
```

#### Subcollection: `questionnaireTemplates/{templateId}/questions/{questionId}`

Questions within a template.

```typescript
{
  id: string;
  text: string;
  order: number;
  questionType: string;
  isBoolAnswer: boolean;
  isTextAnswer: boolean;
  isCheckbox: boolean;
  section: number;
  isOptional: boolean;
  conditionQuestionId?: string; // Reference to another question
  conditionalAnswer?: boolean;
  disabled: boolean;
  defaultAnswer?: string;
  subText?: string;
  parentId?: string; // Reference to parent question
}
```

### `questionnaires/{questionnaireId}`

Instances of questionnaires filled out by patients.

```typescript
{
  templateId: string; // Reference to questionnaireTemplate
  patientId: UserID; // Reference to patients collection
  clientId: UserID; // Reference to clients collection
  assignedDoctorId?: UserID; // Reference to doctors collection
  status: 'created' | 'in-progress' | 'completed' | 'reviewed' | 'assigned';
  hashCode: string;
  savedSection?: number;
  createdAt: timestamp;
  startedAt?: timestamp;
  completedAt?: timestamp;
  isReviewed: boolean;
  complianceReportId?: string; // Reference to the primary report
}
```

#### Subcollection: `questionnaires/{questionnaireId}/answers/{answerId}`

Answers to questions in a questionnaire.

```typescript
{
  questionId: string; // Reference to a question in the template
  textAnswer?: string;
  boolAnswer?: boolean;
}
```

#### Subcollection: `questionnaires/{questionnaireId}/comments/{commentId}`

Comments made by doctors or assistants on specific questions.

```typescript
{
  questionId: string; // Reference to a question
  doctorId?: UserID; // Reference to doctors collection
  assistantId?: UserID; // Reference to assistants collection
  text: string;
  createdAt: timestamp;
  updatedAt?: timestamp;
}
```

#### Subcollection: `questionnaires/{questionnaireId}/complianceReports/{reportId}`

Compliance reports for a questionnaire.

```typescript
{
  doctorId: UserID; // Reference to doctors collection
  date: timestamp;
  respiratorClearance: number;
  respiratorClearanceDescription: string;
  workLoadLimitation: number;
  clearanceBasedOn: string;
  followUp: boolean;
  followUpDescription?: string;
  isAutomaticallyGenerated: boolean;
  isSignedByDoctor: boolean;
  version: number;
  status: 'draft' | 'finalized' | 'archived';
  generationMethod: 'auto' | 'manual';
  pdfUrl?: string;
  signatureUrl?: string;
  signatureDate?: timestamp;
  createdAt: timestamp;
  updatedAt?: timestamp;
  medicalFindings?: Array<{
    category: string;
    severity: number;
    description: string;
  }>;
}
```

### `complianceReports/{reportId}`

Global collection of compliance reports (duplicated from questionnaire subcollections for easier querying).

```typescript
{
  questionnaireId: string; // Reference to questionnaires collection
  patientId: UserID; // Reference to patients collection
  clientId?: UserID; // Reference to clients collection
  doctorId: UserID; // Reference to doctors collection
  date: timestamp;
  respiratorClearance: number;
  respiratorClearanceDescription: string;
  workLoadLimitation: number;
  clearanceBasedOn: string;
  followUp: boolean;
  followUpDescription?: string;
  isAutomaticallyGenerated: boolean;
  isSignedByDoctor: boolean;
  version: number;
  status: 'draft' | 'finalized' | 'archived';
  generationMethod: 'auto' | 'manual';
  pdfUrl?: string;
  signatureUrl?: string;
  signatureDate?: timestamp;
  createdAt: timestamp;
  updatedAt?: timestamp;
  medicalFindings?: Array<{
    category: string;
    severity: number;
    description: string;
  }>;
}
```

### `purchases/{purchaseId}`

Purchase records for clients.

```typescript
{
  clientId: UserID; // Reference to clients collection
  templateId: string; // Reference to questionnaireTemplates collection
  questionnaireQuantity: number;
  unitPrice: number;
  subTotal: number;
  tax: number;
  totalPrice: number;
  isPaid: boolean;
  date: timestamp;
  note?: string;
  transactionId?: string;
  paymentType: string;
  createdAt: timestamp;
}
```

## Key Workflows

### Questionnaire Completion & Compliance Report Generation

1. When a questionnaire's status is changed to "completed":
   - A new compliance report document is created in the questionnaire's subcollection
   - A copy is added to the top-level `complianceReports` collection
   - The questionnaire document is updated with a reference to this report

2. Reports start with `isAutomaticallyGenerated: true` and `status: "draft"`

3. Doctors review and can modify the reports:
   - When signed, `isSignedByDoctor` becomes `true`
   - Status changes to "finalized"
   - Any subsequent changes create a new version

## Common Queries

### Get all reports for a specific patient

```javascript
db.collection('complianceReports').where('patientId', '==', patientId).get()
```

### Get all reports created by a specific doctor

```javascript
db.collection('complianceReports').where('doctorId', '==', doctorId).get()
```

### Get reports requiring follow-up

```javascript
db.collection('complianceReports').where('followUp', '==', true).get()
```

### Get the specific report for a questionnaire

```javascript
db.collection('questionnaires').doc(questionnaireId)
  .collection('complianceReports').doc(reportId).get()
```

## Security Rules

Important security rule patterns for this schema:

```
match /questionnaires/{questionnaireId}/complianceReports/{reportId} {
  allow read: if request.auth != null && 
    (hasRole('doctor') || hasRole('admin') || 
     request.auth.uid == resource.data.patientId);
  allow write: if request.auth != null && 
    (hasRole('doctor') || hasRole('admin'));
}

match /complianceReports/{reportId} {
  allow read: if request.auth != null && 
    (hasRole('doctor') || hasRole('admin') || 
     request.auth.uid == resource.data.patientId);
  allow write: if request.auth != null && 
    (hasRole('doctor') || hasRole('admin'));
}
```

## Schema Evolution

This schema represents a migration from SQL to Firestore. Key improvements include:

1. Hierarchical data structure with subcollections
2. No complex joins required
3. Denormalization for role-specific user data
4. Global collections with duplicate data for easier querying
5. Custom questionnaire template creation by admins and doctors
