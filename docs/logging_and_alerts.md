# Logging and Alerts Framework

This document outlines the usage of the logging and alerts framework implemented in the application. These frameworks provide a standardized way to log user interactions and display alerts to users.

## Table of Contents

1. [User Interaction Logging](#user-interaction-logging)
2. [Alerts Management](#alerts-management)
3. [Integration Guidelines](#integration-guidelines)
4. [Configuration](#configuration)
5. [Examples](#examples)

## User Interaction Logging

The logging framework provides a consistent way to log user interactions, system events, API calls, and other important activities for debugging, auditing, and analytics purposes.

### Key Features

- Multiple log levels: DEBUG, INFO, WARNING, ERROR
- Categorized logging: USER_INTERACTION, SYSTEM, AUTHENTICATION, etc.
- Automatic user identification (when logged in)
- Optional metadata for detailed logging
- Console output for development
- Firestore storage for production

### Usage

```tsx
// In a component
import { useLogging } from 'contexts/LoggingContext';

const MyComponent = () => {
  const logging = useLogging();
  
  const handleClick = () => {
    // Log a simple message
    logging.info('User clicked button', 'MyComponent');
    
    // Log a user interaction with metadata
    logging.logUserInteraction(
      'click', 
      'MyComponent', 
      {
        action: 'submit-form',
        targetId: 'user-profile',
        targetType: 'form',
        success: true,
        additionalData: { formData: { name: 'John' } }
      }
    );
    
    // Log an API call
    logging.logApiOperation(
      '/api/users', 
      'GET', 
      true, 
      200, 
      undefined, 
      { responseTime: 120 }
    );
  };
  
  // Error logging
  try {
    // Some code that might fail
  } catch (error) {
    logging.error('Failed to process data', 'MyComponent', { error });
  }
  
  return <Button onClick={handleClick}>Click Me</Button>;
};
```

## Alerts Management

The alerts framework provides a way to display notifications to users from various sources, including system events, user actions, and database triggers.

### Key Features

- Multiple alert severities: INFO, SUCCESS, WARNING, ERROR
- Multiple sources: SYSTEM, USER, DATABASE
- Real-time alert updates via Firestore
- Read/unread status tracking
- Configurable display options
- Support for action links in alerts

### Usage

```tsx
// In a component
import { useAlerts } from 'contexts/AlertContext';

const MyComponent = () => {
  const { success, error, warning, info, createAlert } = useAlerts();
  
  const handleSuccess = () => {
    // Simple success alert
    success('Operation Successful', 'Your data has been saved.');
    
    // With custom options
    success('Operation Successful', 'Your data has been saved.', {
      autoHideDuration: 3000, // 3 seconds
      preventDuplicates: true
    });
  };
  
  const handleError = () => {
    error('Error', 'Failed to save data. Please try again.');
  };
  
  // Create a custom alert
  const createCustomAlert = () => {
    createAlert(
      'Custom Alert', 
      'This is a custom alert with metadata',
      AlertSeverity.INFO,
      AlertSource.SYSTEM,
      { customData: 'value' },
      { requireAcknowledgement: true }
    );
  };
  
  return (
    <div>
      <Button onClick={handleSuccess}>Trigger Success</Button>
      <Button onClick={handleError}>Trigger Error</Button>
    </div>
  );
};
```

## Integration Guidelines

When integrating the logging and alerts framework into existing components, follow these guidelines:

1. **Component Identification**: Always provide a component name for logs to make debugging easier.
2. **Meaningful Messages**: Write clear, actionable log messages that provide context.
3. **Appropriate Log Levels**: Use the appropriate log level based on severity.
4. **Metadata**: Include relevant metadata without sensitive information.
5. **User Interactions**: Log significant user interactions that may be useful for analytics or auditing.
6. **Error Handling**: Always log errors with full context.

## Configuration

The logging and alerts frameworks can be configured through environment variables:

```
# Logging Configuration
VITE_ENABLE_FIRESTORE_LOGGING=true    # Enable/disable Firestore logging
VITE_LOG_LEVEL=INFO                   # Minimum log level for console output

# Alerts Configuration
VITE_ALERTS_AUTO_HIDE_DURATION=5000   # Default auto-hide duration for alerts
```

## Examples

### Logging User Login

```tsx
// In a login component
const handleLogin = async (credentials) => {
  try {
    const result = await authService.login(credentials);
    
    logging.logUserInteraction('login', 'LoginForm', {
      action: 'user-login',
      success: true,
      additionalData: { userId: result.user.id }
    });
    
    alerts.success('Login Successful', 'Welcome back!');
    navigate('/dashboard');
  } catch (error) {
    logging.logUserInteraction('login', 'LoginForm', {
      action: 'user-login',
      success: false,
      errorMessage: error.message
    });
    
    alerts.error('Login Failed', error.message);
  }
};
```

### Logging Form Submissions

```tsx
const handleSubmit = async (values) => {
  logging.info('Form submission started', 'UserProfileForm', { formValues: values });
  
  try {
    await userService.updateProfile(values);
    
    logging.logUserInteraction('submit', 'UserProfileForm', {
      action: 'update-profile',
      success: true
    });
    
    alerts.success('Profile Updated', 'Your profile has been successfully updated.');
  } catch (error) {
    logging.error('Profile update failed', 'UserProfileForm', { error, values });
    
    alerts.error('Update Failed', 'Failed to update your profile. Please try again.');
  }
};
```

### Database Alerts

Alerts can also be triggered from database events, such as new notifications, messages, or system updates:

```tsx
// In a notifications service
const subscribeToNotifications = (userId) => {
  // ... Firebase subscription logic ...
  
  onSnapshot(query, (snapshot) => {
    snapshot.docChanges().forEach((change) => {
      if (change.type === 'added') {
        const notification = change.doc.data();
        
        // Create an alert from the notification
        alertService.createAlert(
          notification.title,
          notification.message,
          notification.severity || AlertSeverity.INFO,
          AlertSource.DATABASE,
          { notificationId: change.doc.id },
          { preventDuplicates: true }
        );
      }
    });
  });
};
``` 