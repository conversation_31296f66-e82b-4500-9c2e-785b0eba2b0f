# LoggedInUserContext

The `LoggedInUserContext` provides a centralized way to access the currently logged-in user's data throughout the application. It combines authentication information from Firebase Auth with the user's profile data from Firestore.

## Features

- Automatically fetches and caches user data based on the authenticated Firebase user
- Provides loading and error states for UI feedback
- Exposes a method to manually refresh user data when needed
- Centralizes user data retrieval logic to avoid duplication across components

## Usage

### Accessing User Data

To access the current user's data in any component:

```tsx
import { useLoggedInUser } from '[contexts]/LoggedInUserContext';

function UserProfileComponent() {
  const { userData, isLoading, error } = useLoggedInUser();

  if (isLoading) {
    return <div>Loading user data...</div>;
  }

  if (error) {
    return <div>Error loading user data: {error.message}</div>;
  }

  if (!userData) {
    return <div>No user logged in</div>;
  }

  return (
    <div>
      <h1>Welcome, {userData.firstName} {userData.lastName}</h1>
      <p>Email: {userData.email}</p>
      <p>Role: {userData.role}</p>
      {/* Access other user properties as needed */}
    </div>
  );
}
```

### Refreshing User Data

If you need to refresh the user data (for example, after updating the user's profile):

```tsx
import { useLoggedInUser } from '[contexts]/LoggedInUserContext';

function UserProfileEditor() {
  const { userData, refreshUserData } = useLoggedInUser();

  const handleSaveProfile = async () => {
    // Save profile changes to the database
    await updateUserProfile(userData.id, updatedData);
    
    // Refresh the user data to reflect the changes
    await refreshUserData();
    
    // Show success message
    showSuccessMessage('Profile updated successfully');
  };

  // Component rendering
}
```

## Integration with Other Contexts

The `LoggedInUserContext` works alongside other contexts in the application:

1. It depends on the `FirebaseContext` (accessed via `useAuth` hook) to get the authenticated user
2. It's positioned in the component tree to be available to other contexts like `RoleProvider`
3. Components can use both `useAuth()` for auth operations and `useLoggedInUser()` for user data

## Implementation Details

The context combines two key pieces of information:
1. Firebase authentication state (from `useAuth` hook)
2. User document data from Firestore (via `getCurrentUserData`)

This provides a complete user profile with both authentication details and application-specific user data in one place. 