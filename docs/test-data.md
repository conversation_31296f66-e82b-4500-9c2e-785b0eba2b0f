# Test Accounts and Test Data

This document describes all test accounts and sample data generated for E2E testing and development. Use these credentials and IDs for testing, automation, and QA.

## Test User Accounts

| Role         | Email                     | UID                                 | Example Password |
|--------------|---------------------------|-------------------------------------|------------------|
| Admin        | <EMAIL>    | Xg5xWSfpI6Oft9sX4PiMIN7iTw32        | password123      |
| ClinicAdmin  | <EMAIL> | qC6ojRbxFAQkEySg8fyBEcSvFIY2     | password123      |
| Doctor       | <EMAIL>   | bo8KOWklVZPpFB1vFo6P3kot8mx2        | password123      |
| Client       | <EMAIL>   | jEu9KHCLBNTBIbgsbZIgAe2GkN32        | password123      |
| Patient      | <EMAIL>  | bro9Bl1QQUWD8gdnDWdfOirLHuh1        | password123      |

All users have realistic profile fields (name, phone, address, etc.) and role-specific nested data.

## Questionnaire Templates

| Template Name           | Template ID         |
|------------------------|---------------------|
| Respiratory Assessment  | template-1-tcgf2q   |
| Asthma Control         | template-2-vd2e87   |

- Created by: Doctor (bo8KOWklVZPpFB1vFo6P3kot8mx2)
- Each template contains sample questions (boolean, text, number types)

## Purchases

| Purchase ID         | Client UID                       | Template ID         | Quantity | Status |
|---------------------|----------------------------------|---------------------|----------|--------|
| purchase-3km3bl     | jEu9KHCLBNTBIbgsbZIgAe2GkN32     | template-1-tcgf2q   | 5        | Paid   |

- Purchases reference the Client and a Template

## Questionnaires

| Questionnaire ID      | Patient UID                     | Template ID         | Doctor UID                        | Status   |
|----------------------|----------------------------------|---------------------|------------------------------------|----------|
| questionnaire-2wfzeq | bro9Bl1QQUWD8gdnDWdfOirLHuh1     | template-1-tcgf2q   | bo8KOWklVZPpFB1vFo6P3kot8mx2       | Assigned |

- Questionnaires reference the Template, Patient, Client, and Doctor
- Questions and metadata match the template

---

**Note:**
- All IDs and UIDs are generated and linked automatically by the E2E setup scripts.
- To regenerate this data, run: `npm run e2e:run-all`
- If you add more test users, templates, purchases, or questionnaires, update this document accordingly.

---

_Last updated: 2025-04-17_
