# AI-Driven End-to-End Test Writing Instruction Set

This document outlines a set of instructions for the AI to autonomously generate and maintain end-to-end (E2E) tests for the application. The focus is on writing and organizing the tests for various features and user roles without involving CI/CD tools or external integrations.

---

## 1. Analyze Application Features and User Roles
- **Understand Application Structure:**  
  - Parse the codebase or documentation to identify core features (e.g., authentication, dashboard, featureX).
  - Determine key UI components and user flows.
- **Identify User Roles:**  
  - Enumerate all roles (Admins, Clinic Admins, Doctors, Clients, Patients) along with their permissions and unique workflows.
- **Map Features to Roles:**  
  - For each feature, identify which roles have access and what specific behavior is expected.

## 2. Establish a Modular Test Structure
- **Modular Organization:**  
  - Create modules for each application feature (e.g., `auth`, `dashboard`, `featureX`).
  - Within each module, establish separate directories for common tests and role-specific tests (e.g., `roles/admin`, `roles/doctor`).
- **Page Object Models:**  
  - Develop reusable page object models that encapsulate UI selectors and actions.
  - Ensure page objects are updated automatically when the UI changes.

## 3. Writing the Tests
- **Test Scenario Generation:**  
  - Generate test cases covering both positive and negative scenarios.
  - Include tests that verify proper role-based access (e.g., ensuring restricted access for Clients or Patients).
- **Consistent Naming and Structure:**  
  - Follow a consistent naming convention for test files and methods (e.g., `adminDashboard.spec.js`).
  - Organize tests logically so that each test file reflects the feature and role it targets.
- **Assertions and Validations:**  
  - Use clear assertions to validate that the application behaves as expected.
  - Verify UI elements, navigation flows, and data integrity specific to each user role.
- **Error Handling:**  
  - Incorporate error handling strategies (e.g., try-catch blocks) to capture and log errors.
  - Provide detailed error messages and capture contextual information (such as screenshots or logs) for debugging purposes.

## 4. Self-Debugging and Test Optimization
- **Error Pattern Recognition:**  
  - Analyze test failures to distinguish between application issues and test script errors.
  - Automatically flag recurring issues and suggest or implement modifications.
- **Dynamic Adjustments:**  
  - Adjust wait times, retry mechanisms, or test sequences dynamically in response to transient issues.
- **Test Maintenance:**  
  - Regularly update tests to reflect changes in UI elements or business logic.
  - Refactor obsolete or flaky tests to maintain clarity and efficiency.

## 5. Role-Based Testing Considerations
- **Custom Scenarios for Each Role:**  
  - Write tests that are tailored to the specific functionalities available to each user role.
  - Validate that restricted functionalities are inaccessible to unauthorized roles.
- **Data-Driven Testing:**  
  - Use role-specific test data to simulate realistic user interactions.
  - Ensure the tests verify that each role's user journey meets the expected outcomes.

## 6. Documentation and Consistency
- **Inline Documentation:**  
  - Comment test scripts and helper functions to explain their purpose and expected behavior.
- **Enforce Standards:**  
  - Ensure all tests adhere to the standardized folder structure and naming conventions.
  - Regularly review and refactor test code to maintain high readability and ease of maintenance.

---

By following these instructions, the AI will autonomously generate, update, and optimize a robust set of E2E tests for the application. This will ensure comprehensive coverage of application features and role-based access scenarios throughout the development cycle.
