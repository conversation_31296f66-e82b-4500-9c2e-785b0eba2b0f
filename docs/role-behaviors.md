# Role-Based Behaviors Guide

This document enumerates **allowed** and **forbidden** actions for each user role. Use these as test scenario checkpoints when automating RBAC or end‑to‑end tests.

---

## Admin

**Allowed:**
- Access **all** admin routes under `/trq/admin`:
  - Dashboard (`/trq/admin`, `/trq/admin/dashboard`, `/trq/admin/trq-dashboard`)
  - User management (`/trq/admin/user/:id`)
  - Role management (`/trq/admin/roles`, `/trq/admin/role-management`)
  - Permissions management (`/trq/admin/permissions`)
  - RBAC debug (`/trq/admin/rbac-debug`)
  - Import utilities (`/trq/admin/fix-imports`)
- Manage any Firestore data: create/update/delete users, roles, system settings.

**Forbidden:**
- As a logical constraint, **Admin** should _not_ be limited by UI-level guards. However, they cannot fill questionnaires as patients (no `/trq/questionnaire/:id` submission).

---

## ClinicAdmin

**Allowed:**
- Access clinic‑specific dashboard (alias of admin UI scoped to clinics).
- View and manage users within their clinic (clients, patients, assistants).
- Assign doctors to their clinic and manage clinic settings.
- View clinic questionnaires and reports.

**Forbidden:**
- Cannot access global system settings (e.g. global role management, permissions management).
- Cannot use RBAC debug or import utilities.

---

## Doctor

**Allowed:**
- Access doctor dashboard (`/trq/doctor`).
- View assigned patients and their details.
- Assign questionnaires to patients.
- View and review completed questionnaires.
- Generate compliance reports.

**Forbidden:**
- Cannot access any `/trq/admin/*` routes.
- Cannot manage user roles or system settings.

---

## Client

**Allowed:**
- Access client dashboard (`/trq/client`).
- View purchase history and purchase new questionnaire templates.
- Assign purchased questionnaires to their patients.
- View responses aggregated for their clients.

**Forbidden:**
- Cannot manage system roles or permissions.
- Cannot access other clients’ data.

---

## Patient

**Allowed:**
- Access patient dashboard (`/trq/patient`).
- View and complete assigned questionnaires (`/trq/questionnaire/:id`).
- View own questionnaire history and compliance reports.

**Forbidden:**
- Cannot view or edit other users’ data.
- Cannot access any `/trq/admin/*`, `/trq/doctor/*`, or `/trq/client/*` routes.

---

**Test Scenario Examples:**
1. **Doctor access denial:** Doctor tries `/trq/admin/permissions` → should redirect or show “Unauthorized.”
2. **Patient scope:** Patient visits `/trq/questionnaire/:otherPatientId` → should be blocked.
3. **Client purchase:** Client attempts to access `/trq/admin/roles` → forbidden.
4. **ClinicAdmin boundary:** ClinicAdmin tries to reassign a user from another clinic → should fail.
5. **Admin full access:** Admin performs a user role change via `/trq/admin/role-management` → success.

Use this guide to build AI-driven test scripts or manual test cases, ensuring RBAC rules hold across your application.
