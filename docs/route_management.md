# Route Management

This document describes how to use the centralized route management system in the application.

## Overview

All application routes are defined in a single place: `src/Routing/appRoutes.ts`. This ensures consistency in route structure and makes route management more reliable.

## Route Structure

Routes are organized by module/feature and follow these naming conventions:
- All paths use lowercase plural names for resources (e.g., `/trq/patients`, `/trq/doctors`)
- Home/dashboard routes use the pattern `/trq/{module}/home` (e.g., `/trq/patients/home`)
- Detail routes use the pattern `/trq/{module}/{id}` (e.g., `/trq/patients/123`)

## Using Routes in Components

### Importing

```tsx
import { ROUTES } from 'path/to/Routing/appRoutes';
```

Or import specific parts:

```tsx
import { ROUTES, getDashboardUrlForRole } from 'path/to/Routing/appRoutes';
```

### Navigation

Use the route constants with the `navigate` function:

```tsx
import { useNavigate } from 'react-router-dom';
import { ROUTES } from 'path/to/Routing/appRoutes';

const MyComponent = () => {
  const navigate = useNavigate();
  
  const handleGoToPatients = () => {
    navigate(ROUTES.PATIENTS.BASE);
  };
  
  const handleViewPatient = (id: string) => {
    navigate(ROUTES.PATIENTS.DETAILS(id));
  };
  
  // ...
};
```

### Links

Use the route constants with the `Link` component:

```tsx
import { Link } from 'react-router-dom';
import { ROUTES } from 'path/to/Routing/appRoutes';

const MyComponent = () => {
  return (
    <nav>
      <Link to={ROUTES.DASHBOARDS.PATIENT}>Dashboard</Link>
      <Link to={ROUTES.PATIENTS.BASE}>Patients</Link>
      <Link to={ROUTES.QUESTIONNAIRES.MY_QUESTIONNAIRES}>My Questionnaires</Link>
    </nav>
  );
};
```

### Breadcrumbs

Use the route constants with the `Breadcrumbs` component:

```tsx
import Breadcrumbs from '[components]/extended/Breadcrumbs';
import { ROUTES } from 'path/to/Routing/appRoutes';

const MyComponent = () => {
  return (
    <Breadcrumbs
      links={[
        {
          title: 'Dashboard',
          to: ROUTES.DASHBOARDS.PATIENT
        },
        {
          title: 'Patients',
          to: ROUTES.PATIENTS.BASE
        },
        {
          title: 'Patient Details'
        }
      ]}
    />
  );
};
```

### Role-Based Dashboard URLs

Use the `getDashboardUrlForRole` function to get the dashboard URL for a specific role:

```tsx
import { getDashboardUrlForRole } from 'path/to/Routing/appRoutes';
import { Role } from 'path/to/Role';

const dashboardUrl = getDashboardUrlForRole(Role.Patient);
```

## Adding New Routes

When adding new features that require routes:

1. Add the new routes to the appropriate section in `src/Routing/appRoutes.ts`
2. Follow the existing pattern for naming and structure
3. Export the new routes through the `ROUTES` object

Example:

```typescript
// New feature routes
const NEW_FEATURE = {
  BASE: `${TRQ_BASE}/new-feature`,
  DETAILS: (id: string) => `${TRQ_BASE}/new-feature/${id}`,
  ADD: `${TRQ_BASE}/new-feature/add`,
};

// Update the ROUTES object
export const ROUTES = {
  // ... existing routes
  NEW_FEATURE,
};
```

## Benefits

Using the centralized route management system provides several benefits:

- **Consistency**: All routes follow the same structure and naming conventions
- **Maintainability**: Routes are defined in a single place, making them easy to update
- **Type Safety**: TypeScript provides autocomplete and type checking for routes
- **Refactoring**: Changing a route path only requires updating it in one place
- **Documentation**: The route structure is self-documenting and easy to understand 