# RBAC Troubleshooting Guide

This document outlines common issues with Role-Based Access Control (RBAC) implementation and how to troubleshoot them.

## Common RBAC Issues

1. **Permission Denied for Admin Users**
   - Admin users should have access to all resources
   - If admin users are getting denied, check the hasPermission implementation

2. **Incorrect Import Paths**
   - Make sure services/hooks are imported from the correct paths
   - The feature structure uses [features]/RBAC/... paths which can be inconsistent

3. **Role Not Being Detected**
   - Make sure the RoleContext is working correctly
   - Verify that assumedRole is being set properly

4. **Silent Redirects to Dashboard**
   - Routes might be redirecting to the dashboard without showing an unauthorized page
   - Check for silent redirects in the route guards

## Debug Permission Route

We've created a `DebugPermissionRoute` component to help troubleshoot permission issues:

```tsx
// src/[features]/RBAC/DebugPermissionRoute.tsx
const DebugPermissionRoute: React.FC<DebugPermissionRouteProps> = ({ 
  children, 
  resource, 
  permissionAction, 
  redirectTo = '/trq/unauthorized',
  bypassForAdmin = true
}) => {
  // ...implementation...
}
```

### Features

1. **Enhanced Logging**
   - Logs all permission checks to the console with clear visual indicators
   - Shows the role, resource, action, and result of each check
   - Logs when the component mounts and provides path information
   - Uses console.error for denied permissions to make them stand out

2. **Admin Bypass**
   - Optional bypass for admin users (on by default)
   - Ensures admins can always access resources for testing

3. **Detailed Error Information**
   - Passes detailed information to the unauthorized page including:
     - Resource being accessed
     - Action attempted
     - User's role
     - Original path
     - Detailed error message

### How to Use

Replace `PermissionRoute` with `DebugPermissionRoute` in your routes:

```tsx
<DebugPermissionRoute 
  resource={RESOURCES.QUESTIONNAIRES} 
  permissionAction={ACTIONS.LIST} 
  bypassForAdmin={true}
>
  <Outlet />
</DebugPermissionRoute>
```

## Enhanced Unauthorized Page

The unauthorized page has been enhanced to provide more detailed information about why access was denied:

1. **Visual Error Indicators**
   - Clear 401 error status
   - Error icon and color-coded message

2. **Detailed Error Information**
   - Shows the specific resource being accessed
   - Shows the user's role
   - Shows the action being attempted
   - Shows the original path that was requested

3. **Navigation Options**
   - Go back to previous page
   - Go to dashboard

## Troubleshooting Process

When experiencing RBAC issues:

1. **Enable Debug Mode**
   - Replace all PermissionRoute components with DebugPermissionRoute
   - Set bypassForAdmin=true to ensure admin access works

2. **Check Console Logs**
   - Look for logs from [DebugPermissionRoute]
   - Check for ❌ indicators in the logs
   - Verify the roles and permissions being checked

3. **Check Unauthorized Page**
   - When redirected to the unauthorized page, check the detailed error information
   - Use this to identify which permission is failing

4. **Check Import Paths**
   - Verify that hooks and services are being imported from the correct paths
   - Fix any import path inconsistencies

5. **Verify Role Context**
   - Make sure the RoleContext is providing the correct role
   - Check if the user has the role you expect

## Path Issues

The application has standardized on the `[features]/RBAC/...` import style with square brackets. 

> **IMPORTANT UPDATE:** We have standardized on the `[features]/RBAC/...` syntax with square brackets. 
> The `@features` prefix has been deprecated and should no longer be used.
> All `@features` functionality has been migrated to `[features]` with enhanced implementations.

### Migrating from @features to [features]

If you encounter files using `@features/RBAC/...` imports, you should:

1. Update all import statements to use `[features]/RBAC/...` instead
2. Run the FixImports tool (available at "/trq/admin/fix-imports") to help with this migration
3. Ensure that all references to RBAC components are updated consistently

Example:

```tsx
// OLD (deprecated)
import { useRole } from '@features/RBAC/RoleContext';

// NEW (correct)
import { useRole } from '[features]/RBAC/RoleContext';
```

## Temporary Solutions

If you need quick access while debugging:

1. Remove permission routes temporarily
2. Use the DebugPermissionRoute with bypassForAdmin=true
3. Fix any incorrect imports in hooks and services

## Next Steps

After resolving the immediate issues:

1. Update all import paths for consistency
2. Add unit tests for permission checks
3. Document the correct import patterns
4. Add better error handling for permission checks 