# Project Structure

## Module Organization

The project is organized into modules (capital first letter) and collections (surrounded by square brackets):

```
src/
├── [components]/         # Shared UI components
├── [contexts]/           # React contexts
├── [constants]/          # Constants and configuration values
├── [hooks]/              # Custom React hooks
├── [services]/           # Service modules for external integrations
├── [types]/              # TypeScript type definitions
├── [utils]/              # Utility functions
│
├── Admin/                # Admin module
│   ├── [pages]/          # Admin views
│   ├── [components]/     # Admin-specific components
│   └── [services]/       # Admin-specific services
│
├── Authentication/       # Authentication module
│   ├── [pages]/          # Authentication views
│   ├── [components]/     # Authentication-specific components
│   └── [services]/       # Authentication-specific services
│
├── Patient/              # Patient module
│   ├── [pages]/          # Patient views
│   ├── [components]/     # Patient-specific components
│   └── [services]/       # Patient-specific services
│
├── Client/               # Client module
│   ├── [pages]/          # Client views
│   ├── [components]/     # Client-specific components
│   └── [services]/       # Client-specific services
│
├── Doctor/               # Doctor module
│   ├── [pages]/          # <PERSON> views
│   ├── [components]/     # Doctor-specific components
│   └── [services]/       # Doctor-specific services
│
├── ClinicAdmin/          # Clinic admin module
│   ├── [pages]/          # Clinic admin views
│   ├── [components]/     # Clinic admin-specific components
│   └── [services]/       # Clinic admin-specific services
│
├── Questionnaires/       # Questionnaires module
│   ├── [pages]/          # Questionnaire views
│   ├── [components]/     # Questionnaire-specific components
│   └── [services]/       # Questionnaire-specific services
│
├── RBAC/                 # Role-based access control module
│   ├── [pages]/          # RBAC views
│   ├── [components]/     # RBAC-specific components
│   ├── [hooks]/          # RBAC-specific hooks
│   └── [services]/       # RBAC-specific services
│
├── Alerts/               # Alerts module
│   ├── [pages]/          # Alert views
│   ├── [components]/     # Alert-specific components
│   └── [services]/       # Alert-specific services
│
├── Activity/             # Activity tracking module
│   ├── [pages]/          # Activity views
│   ├── [components]/     # Activity-specific components
│   └── [services]/       # Activity-specific services
│
├── Notifications/        # Notifications module
│   ├── [pages]/          # Notification views
│   ├── [components]/     # Notification-specific components
│   └── [services]/       # Notification-specific services
│
├── Localization/         # Localization module
│   ├── [pages]/          # Localization views
│   ├── [components]/     # Localization-specific components
│   └── [services]/       # Localization-specific services
│
├── Navigation/           # Navigation module
│   ├── [pages]/          # Navigation views
│   ├── [components]/     # Navigation-specific components
│   └── [services]/       # Navigation-specific services
│
├── Layout/               # Layout module
│   ├── [pages]/          # Layout views
│   ├── [components]/     # Layout-specific components
│   └── [services]/       # Layout-specific services
```

## Design Patterns

### Modules
- **Capital First Letter**: Modules are business domains or major feature areas
- Each module contains its own UI views, components, and business logic
- Modules can be independently developed and maintained

### Collections
- **[Square Brackets]**: Collections are shared resources or utilities
- Collections are organized by technical concern rather than business domain
- Collections can be used across multiple modules
- All collections have square brackets, even when inside a module

### File Organization
- No index.tsx files - all components have descriptive filenames
- Flattened folder structure - minimal nesting for easier navigation
- Shared components moved to common locations

## Structure Maintenance

### Updating this Document
- **After Every Change**: This document should be updated whenever the folder structure changes
- Add new modules/collections or adjust existing ones to reflect the current state
- Keep the documentation in sync with the actual codebase structure

### Verifying Structure
You can generate a current directory structure using:
```bash
# To list directories only
find src -type d -not -path "*/node_modules/*" | sort

# For a more visual representation
find src -type d -not -path "*/node_modules/*" | sort | sed -e "s/[^-][^\/]*\// |/g" -e "s/|\([^ ]\)/|-\1/"
```

### When to Update
- When adding a new module
- When adding a significant new collection
- When changing the organization pattern
- When moving components between modules
- When flattening nested structures 