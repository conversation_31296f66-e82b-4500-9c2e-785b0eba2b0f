# E2E Test Folder Structure

This document outlines the recommended folder structure for End-to-End (E2E) tests using <PERSON><PERSON>, designed to align with the application's modular source code structure (`src/ModuleName/`).

```
e2e/
├── config/
│   ├── environment.json        # Environment-specific settings (URLs, credentials, etc.)
│   ├── config.ts               # Global configuration for tests (e.g., timeouts, reporters)
│   └── trq-e2e-firebase-config.ts # Firebase config specifically for E2E tests
│
├── modules/                    # Top-level directory for module-specific tests
│   │
│   ├── Authentication/         # Tests related to the Authentication module (src/Authentication/)
│   │   ├── pageObjects/        # Page Object Models specific to Authentication
│   │   │   └── login.page.ts
│   │   └── tests/              # Test specifications for Authentication
│   │       └── login.spec.ts
│   │
│   ├── UserManagement/         # Example for User Management module (src/UserManagement/)
│   │   ├── pageObjects/
│   │   │   └── userManagement.page.ts
│   │   └── tests/
│   │       └── userManagement.spec.ts # Could contain CRUD tests for users
│   │
│   └── ModuleX/                # Placeholder for another application module (e.g., Clinic, Patient)
│       ├── pageObjects/
│       │   └── moduleX.page.ts
│       └── tests/
│           └── moduleX.spec.ts
│
├── setup/
│   ├── global-setup.ts         # Global setup script (e.g., initial login, seeding)
│   └── auth-setup.ts           # Specific setup for authentication states if needed
│
├── utils/
│   └── helper.ts               # Utility functions, custom commands, shared logic
│
├── scripts/                    # Helper scripts (e.g., creating test users)
│   └── create-test-users.ts
│
├── playwright-report/          # Default output directory for HTML reports
│
└── test-results/               # Default output directory for test artifacts (traces, videos, screenshots)

```

**Key Principles:**

*   **Module Alignment:** The `e2e/modules/` directory mirrors the structure under `src/`. Each sub-directory within `e2e/modules/` corresponds to a specific application module (e.g., `Authentication`, `UserManagement`).
*   **Page Objects:** Encapsulate interactions with specific pages or components within the corresponding module's `pageObjects/` directory.
*   **Tests:** Test specifications (`.spec.ts` files) reside in the `tests/` directory of their respective module.
*   **Configuration:** Global and environment-specific configurations are kept in `e2e/config/`. Specific Playwright config files (e.g., `playwright.config.ts`, `playwright.role-login-tests.config.ts`) are at the `e2e/` root.
*   **Setup:** Global setup logic is placed in `e2e/setup/`.
*   **Utilities:** Shared helper functions go into `e2e/utils/`.
*   **Scripts:** Standalone scripts for test support (like data generation) are in `e2e/scripts/`.

This structure promotes organization, maintainability, and clear ownership of tests by aligning them directly with the application's modules.
