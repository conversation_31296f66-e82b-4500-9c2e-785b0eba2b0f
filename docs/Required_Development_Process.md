
Before implementing anything




MODELS

Identify the Models/Interfaces involved. If they do not exist Identify existing models that can be extended. If that is not possible, only then create new models. Do not perform destructive actions on models without an explicit request. Models are the foundation of the application and should be the source of truth.

COLLECTIONS

The collections, depicted by having square brackets around the foler name, are groups of a similiar type of thing. For Example, [features], [components].

MODULES

Identify which modules/features are going to be impacted. Every folder in the application is either a module or a collection of a certain type. Modules contain all files related to said module or feature. A module can be made up of collections and other modules. The application as a whole is a module and therefore has collections of common things as well as sub modules which are its make up. 

COMPONENTS

Identify which components need to be updated. Identify if this change should be its own component or if it should be incorporated into other components. Identify if we already have a component which does all or most of what we are about to do, somewhere else in the application. Only once we have exhausted other options should we make a custom component.


STUBS

Before writing any actual logic we will stub out any functions we want to add to the application. We will provide their parameters and throw "feature in progress" error to identify that they are in progress.


ROUTES

Do not remove routes unless explicitly asked. Only change routes if explicitly asked or a change requires an existing route to move or vary slightly. Extend routes and keep them consistent in structure.


THEMES
Any UI changes that are made should add appropriate theming as per the applications theme.
