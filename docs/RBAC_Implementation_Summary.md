# RBAC Implementation for Questionnaires and Patients

This document summarizes the implementation of Role-Based Access Control (RBAC) for the Questionnaires and Patients sections of the application.

## Overview

We implemented RBAC following the principle of least privilege, ensuring that users only have access to the resources and actions they need to perform their jobs. The implementation spans multiple layers:

1. **UI Components** - Conditional rendering based on permissions
2. **API/Service Calls** - Permission checks before performing actions
3. **Route Guards** - Protecting routes based on permissions
4. **Error Handling** - Displaying user-friendly unauthorized access pages

## Implementation Details

### 1. Component Level Permission Checks

We added the `usePermission` hook to UI components to conditionally render UI elements:

```tsx
// Example from AllQuestionnaires.tsx
const permissions = usePermission();

// Check permission before showing button
{canCreate && (
  <Button 
    variant="outlined" 
    color="primary" 
    onClick={() => setTemplateDialogOpen(true)}
  >
    Create from Template
  </Button>
)}
```

This ensures that UI controls are only shown to users who have appropriate permissions.

### 2. Action/Service Level Permission Checks

We added permission checks before executing actions:

```tsx
// Example from AllQuestionnaires.tsx
const handleDelete = async (id: string) => {
  if (permissions.canDeleteQuestionnaire()) {
    try {
      await deleteQuestionnaire(id);
      // Update state
    } catch (error) {
      console.error('Error deleting questionnaire:', error);
    }
  }
};
```

This prevents unauthorized actions even if a user attempts to bypass UI controls.

### 3. Route Level Protection

We implemented route protection using the `PermissionRoute` component:

```tsx
// Example from TRQRoutes.tsx
{
  path: 'questionnaires',
  element: (
    <PermissionRoute resource={RESOURCES.QUESTIONNAIRES} permissionAction={ACTIONS.LIST}>
      <Outlet />
    </PermissionRoute>
  ),
  children: [
    // Child routes
  ]
}
```

This ensures that users cannot access protected routes without appropriate permissions.

### 4. Fallback UI for Unauthorized Access

We added appropriate feedback when users don't have permissions:

```tsx
// Example from AllPatients.tsx
if (!permissions.canListPatients()) {
  return (
    <MainCard>
      <Typography variant="h3" color="error" align="center">
        You do not have permission to view patients
      </Typography>
    </MainCard>
  );
}
```

### 5. Unauthorized Page

We created a dedicated Unauthorized (401) page that is displayed when a user attempts to access a route they don't have permission for. The page:

- Displays a clear error message
- Shows which resource the user was trying to access (if available)
- Provides navigation options (go back, go to dashboard)
- Is integrated with the internationalization system

The PermissionRoute component automatically redirects to this page when a permission check fails and passes the resource information as state:

```tsx
// From PermissionRoute.tsx
if (!hasRequiredPermission) {
  return (
    <Navigate 
      to="/trq/unauthorized" 
      state={{ 
        resource: resource,
        action: permissionAction
      }} 
    />
  );
}
```

The unauthorized page then uses this information to display a more specific error message:

```tsx
// From TRQUnauthorized.tsx
const resourceMessage = resource ? 
  `${action ? `${action} ` : ''}${resource}` : 
  '';

// ...

<Typography variant="body1">
  {`You don't have permission to access ${resourceMessage ? `the ${resourceMessage}` : 'this resource'}.`}
</Typography>
```

## Best Practices Followed

1. **Least Privilege Principle** - Users only have access to what they need
2. **Defense in Depth** - Multiple layers of permission checks
3. **Graceful Degradation** - Friendly UI when permissions are missing
4. **Separation of Concerns** - RBAC logic is separated from business logic

## Resources and Permissions

The application uses the following resources and actions for RBAC:

| Resource | Actions |
|----------|---------|
| QUESTIONNAIRES | LIST, READ, CREATE, UPDATE, DELETE |
| PATIENTS | LIST, READ, CREATE, UPDATE, DELETE |

## Next Steps

To further enhance RBAC implementation:

1. Implement RBAC in remaining sections of the application
2. Add logging for permission checks and access attempts
3. Add audit trails for security-relevant actions
4. Implement regular permission reviews
5. Consider adding attribute-based access control for finer-grained permissions 