# Role Assumption Feature

This document explains the Role Assumption feature implemented in the TRQ application, which allows users with higher-level permissions to temporarily assume a lower-level role for testing and debugging purposes.

## Overview

Role Assumption allows an admin or clinic admin to view the application from the perspective of a user with lower permissions. This is useful for:

- Testing role-based features
- Verifying the user experience for different roles
- Troubleshooting permission-related issues
- Training new users

## Role Hierarchy

The role hierarchy in the application is:

```
Admin → Clinic Admin → Doctor → Client → Patient
```

Users can assume roles that are lower in the hierarchy than their actual role:
- **Admin**: Can assume any role.
- **Clinic Admin**: Can assume Doctor, Client, or Patient roles.
- **Doctor**: Can assume Client or Patient roles.
- **Client**: Can assume Patient role only.
- **Patient**: Cannot assume any other roles.

## How It Works

### Technical Implementation

The role assumption feature is implemented using React Context:

1. `RoleContext`: Stores both the user's actual role and their currently assumed role
2. `useRole` hook: Provides access to role information and role assumption functions
3. `AssumeRoleSelector`: UI component that displays in the header for eligible users

The assumed role is persisted in local storage so it remains consistent across page reloads.

### UI Components

1. **Role Section in Header**:
   - Visible only to users who can assume roles
   - Displays either an "Assume Role" button or a chip showing the currently assumed role
   - Compact design to fit in the header

2. **Role Selection Menu**:
   - Opens when the "Assume Role" button is clicked
   - Lists all roles the user can assume
   - Displays user-friendly role names

3. **Revert Button**:
   - Visible when a role is being assumed
   - Allows the user to quickly revert to their actual role

## How to Use

### To Assume a Role

1. Look for the "Assume Role" button in the navigation bar (only visible if you have permission to assume roles)
2. Click the button to open the role selection menu
3. Select the role you wish to assume
4. The UI will update to show the application as it appears to users with that role

### To Revert to Your Actual Role

1. Click the "X" button next to the "Viewing as: [Role]" chip in the navigation bar
2. Your original permissions will be restored immediately

## Security Considerations

- Role assumption is purely client-side and doesn't affect the user's actual permissions in the system
- API calls still use the user's actual role for authentication 
- The role assumption feature is only available to users with appropriate permissions
- The `DebugPermissionRoute` component has an admin bypass feature that ensures admins can always access routes for testing

## Implementation Details

The implementation involves several key files:

1. `src/@features/RBAC/RoleContext.tsx`: Manages role state and provides functions
2. `src/@features/RBAC/RoleAssumption/AssumeRoleSelector.tsx`: UI component for selecting roles 
3. `src/@features/RBAC/[services]/roleService.ts`: Utility functions for role management
4. `src/layout/MainLayout/Header/RoleSection/index.tsx`: Container component for the header

## Troubleshooting

1. **Role selector not appearing in header**:
   - Verify that your user has a role that allows assuming other roles
   - Check that the `RoleSection` component is included in the header
   - Confirm that the paths for imports between components are correct

2. **Unable to assume a different role**:
   - Check browser console for errors
   - Verify that the role you're trying to assume is lower in the hierarchy than your actual role
   - Confirm that the `assumeRole` function in the context is being called correctly

3. **Assumed role not persisting on page reload**:
   - Verify that local storage is available in your browser
   - Check that `localStorage.setItem('assumedRole', role)` is being called when assuming a role
   - Confirm that the `useEffect` for restoring the role on mount is working correctly 