# AI-Assisted Development Plan

This document outlines a **feature-based** development workflow with a three-phase cycle (Planning & Design, Major Implementation, and Refinements & Small Changes) for **each feature**. It also covers how to handle **common or shared components** that multiple features depend on. Use this plan as a guide for working with an AI assistant to keep tasks focused, maintain scope, and ensure a clean, iterative development process.

---

## Table of Contents
1. [Introduction](#introduction)
2. [Feature-Based Phases](#feature-based-phases)
   - [Phase 1: Planning & Design](#phase-1-planning--design)
   - [Phase-2: Major Implementation](#phase-2-major-implementation)
   - [Phase-3: Refinements--small-changes](#phase-3-refinements--small-changes)
3. [Common/Shared Components](#commonshared-components)
   - [Ways to Handle Common/Shared Components](#ways-to-handle-commonshared-components)
4. [Iteration & Documentation](#iteration--documentation)
5. [Example AI Prompts](#example-ai-prompts)
6. [Best Practices & Tips](#best-practices--tips)
7. [Usage Instructions](#usage-instructions)

---

## Introduction

In this plan, **each feature** of your software goes through three phases:

1. **Phase 1**: Planning & Design  
2. **Phase 2**: Major Implementation  
3. **Phase 3**: Refinements & Small Changes  

The development cycle is repeated for **each feature** individually, allowing you to fully plan, build, and refine one feature before moving on (or returning to an earlier phase if needed). This ensures each feature is well-documented, robust, and maintainable.

Additionally, certain components may be **common or shared** across different features. These shared utilities or modules can also follow the same three-phase cycle (treated as “mini-features”) or be developed in parallel with other features as necessary.

---

## Feature-Based Phases

### Phase 1: Planning & Design

**Goals:**
- Clearly define the scope, requirements, and constraints of the current feature.
- Create or update any relevant design documentation (architecture diagrams, data models, API specs).
- Establish acceptance criteria and success metrics.

**AI’s Role:**
- Gather and clarify the feature’s requirements.
- Propose design approaches aligned with the overall project architecture.
- Document designs (class diagrams, interfaces, domain models), but do **not** provide production-level code.

**In-Scope Work:**
- High-level design documents, data flows, interface definitions.
- Draft or prototype if needed to validate design concepts.
- Identifying external dependencies and integration points.

**Out-of-Scope Work:**
- Implementing final/production-level code.
- Making large changes outside the defined feature scope.

---

### Phase 2: Major Implementation

**Goals:**
- Implement the feature’s primary functionality according to the Phase 1 design.
- Keep the code modular and reusable.
- Integrate the feature into the existing system in a clean, maintainable way.
- Update design documents with any changes uncovered during implementation.

**AI’s Role:**
- Write production-level code that aligns with Phase 1 design.
- Provide or suggest robust test strategies (unit, integration) for the feature.
- Avoid introducing unrelated functionalities or design overhauls outside the feature scope.

**In-Scope Work:**
- Coding core modules, APIs, or UI elements for the feature.
- Validating and integrating with other parts of the system as planned.
- Making necessary updates to documentation as the feature evolves.

**Out-of-Scope Work:**
- Adding new features not in the Phase 1 plan.
- Redesigning unrelated areas of the system.
- Handling minor UI/UX tweaks unless critical to the major functionality (these typically go in Phase 3).

---

### Phase 3: Refinements & Small Changes

**Goals:**
- Polish the feature with bug fixes, performance improvements, and minor adjustments.
- Finalize UI/UX details and ensure the feature meets acceptance criteria.
- Avoid major refactors or redesigns—those would prompt a return to Phase 1 or 2.

**AI’s Role:**
- Implement small improvements and bug fixes.
- Ensure stability and seamless integration with the broader system.
- Do not introduce major new functionality or structural changes.

**In-Scope Work:**
- Tweaking logic, layout, or data handling for usability, performance, or aesthetics.
- Correcting any bugs found during testing or user feedback.
- Updating documentation to capture final changes.

**Out-of-Scope Work:**
- Overhauling the feature or core architecture (requires re-entering Phase 1 or 2).
- Adding major new features or functionalities.

---

## Common/Shared Components

### What Are They?
- Reusable utilities, libraries, or infrastructure code used by multiple features.
- UI components (e.g., design systems, shared widgets).
- Core data-layer or service integrations for cross-cutting concerns.

### Why They Matter
- Reduces duplication and promotes consistency across features.
- Simplifies maintenance and speeds up development.

### Ways to Handle Common/Shared Components

1. **Treat Them as “Mini-Features”**  
   - They have the same three-phase cycle: Plan & Design → Major Implementation → Refinements.  
   - **Phase 1**: Outline requirements and design for the shared component/library.  
   - **Phase 2**: Implement production-ready code in a modular way with thorough testing.  
   - **Phase 3**: Fix bugs, polish performance, and finalize documentation.

2. **Develop Them in Parallel with Features**  
   - When a feature requires a common utility (e.g., date parsing, UI widgets), factor it out early.  
   - Conduct a mini-cycle for that component or incorporate it into the feature’s cycle if scope and urgency permit.

3. **Versioning & Stability**  
   - Consider versioning shared components (e.g., `v1`, `v2`) to manage breaking changes.  
   - New or major refactors should be planned as separate, backward-compatible releases or cycles.

---

## Iteration & Documentation

- **Iterative Development**  
  - Each feature (or common component) independently cycles through Phases 1 → 2 → 3.  
  - If significant new requirements or issues arise, you can return to Phase 1 or 2 for that specific item.

- **Documentation Maintenance**  
  - Maintain updated design docs, code-level docs, and user guides at the end of each phase.  
  - Version control helps track changes to both code and documentation.

- **Scope Management**  
  - Keep a backlog or wish list of out-of-scope ideas.  
  - If the AI or team proposes an idea beyond the current feature’s scope, capture it for a future cycle.

- **Definition of Done**  
  - **Phase 1**: Requirements clarified, design documented, acceptance criteria set.  
  - **Phase 2**: Core functionality built, tested, and integrated.  
  - **Phase 3**: All refinements done, feature is stable and meets acceptance criteria.

---

## Example AI Prompts

Below are sample prompts you might give to your AI assistant, specifying both **the feature** (or shared component) and **the current phase** to keep tasks focused.

1. **Phase 1 (Feature)**  
   - “We’re in **Phase 1** for the _User Profile Feature_. Please outline the requirements, data model, and high-level design. Do not produce production code.”

2. **Phase 2 (Feature)**  
   - “We’re now in **Phase 2** for the _User Profile Feature_. Implement the main CRUD functionality for profile data. Provide unit tests, keep it modular, and follow the design from Phase 1.”

3. **Phase 3 (Feature)**  
   - “We’re in **Phase 3** of refining the _User Profile Feature_. Fix minor bugs and optimize performance. Avoid adding new features; just finalize what we have.”

4. **Phase 1 (Shared Component)**  
   - “We need a common logger utility for all features. We’re in **Phase 1**. Propose a design for the logger module and outline how features will consume it.”

5. **Phase 2 (Shared Component)**  
   - “We’re in **Phase 2** for the _Common Logger_. Implement it with multiple log levels (info, warn, error). Include tests showing how other features might integrate.”

6. **Phase 3 (Shared Component)**  
   - “We’re in **Phase 3** of the _Common Logger_. Fix any performance issues and ensure it handles edge cases gracefully. Do not add major new functionality.”

---

## Best Practices & Tips

1. **Clear Phase Identification**  
   - Always specify the current phase (1, 2, or 3) and the item’s name (feature or shared component) when prompting the AI.

2. **Regular Checkpoints & Reviews**  
   - End of Phase 1: Validate design meets requirements and overall architecture.  
   - End of Phase 2: Confirm core implementation is functional and tested.  
   - End of Phase 3: Ensure refinements meet acceptance criteria and the code is ready for production.

3. **Branching & Version Control**  
   - Use a dedicated branch for each feature or shared component.  
   - Merge into `main` or `dev` only once the item completes Phase 3 (or at strategic checkpoints).

4. **Testing Strategy**  
   - Phase 2: Focus on unit and integration testing.  
   - Phase 3: Emphasize final QA, performance checks, and regression testing.

5. **Documentation First**  
   - In Phase 1, detail high-level designs.  
   - Update documents in Phase 2 and Phase 3 as you learn more.  
   - Keep code and documentation in sync to avoid confusion.

6. **Scoping & Backlog**  
   - If new feature ideas emerge that are out of scope for the current phase, add them to your backlog.  
   - Revisit them in a future Phase 1 to design them thoroughly.

---

## Usage Instructions

1. **Add This Document to Your Repository**  
   - Keep it accessible (e.g., in `docs/AI_Assisted_Dev_Plan.md`) so team members know the process.

2. **Adhere to Phases**  
   - At the start of work on each feature or shared component, clearly state:  
     - Which phase you’re in  
     - The name of the feature (or component)  
     - The desired outcome (e.g., design doc, functional code, refinements).

3. **Prompt the AI Correctly**  
   - Provide context: feature name, current phase, and any relevant details about scope or constraints.  
   - Reference the “In-Scope” and “Out-of-Scope” lists to keep the AI on track.

4. **Iterate as Needed**  
   - If major changes are required after Phase 3, circle back to Phase 1 or 2 for that item, documenting why and what changes.

By following these guidelines, you’ll maintain a clear, organized, and iterative workflow that leverages AI assistance effectively without overstepping the defined scope or missing crucial documentation.
