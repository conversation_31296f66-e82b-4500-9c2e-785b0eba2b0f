# Role-Based Menu Structure

This document outlines the menu items available to each role in the TRQ system. The menu structure is designed to provide role-appropriate access to system features while maintaining a clean and intuitive user interface.

## Overview

The TRQ system implements a role-based navigation system where available menu items are dynamically displayed based on the user's role. This ensures that users only see menu options that are relevant to their responsibilities and that they have permission to access.

The role hierarchy (from highest to lowest privilege) is:

1. Admin
2. Clinic Admin
3. Doctor
4. Client
5. Patient

## Menu Items by Role

The following table shows which menu items are available to each role:

| Menu Item                  | Admin | Clinic Admin | Doctor | Client | Patient |
| -------------------------- | :---: | :----------: | :----: | :----: | :-----: |
| Dashboard                  |  ✅   |      ✅      |   ✅   |   ✅   |  ✅\*   |
| All Questionnaires         |  ✅   |              |        |        |         |
| Questionnaires             |       |      ✅      |   ✅   |   ✅   |   ✅    |
| Templates                  |  ✅   |      ✅      |   ✅   |        |         |
| Reports/Compliance Reports |  ✅   |      ✅      |   ✅   |        |         |
| Patients                   |  ✅   |      ✅      |   ✅   |   ✅   |         |
| Clients                    |  ✅   |      ✅      |        |        |         |
| All Users                  |  ✅   |              |        |        |         |
| Clinics                    |  ✅   |              |        |        |         |
| Analytics (under Admin)    |  ✅   |              |        |        |         |
| All Purchases              |  ✅   |              |        |        |         |
| Admin Dashboard            |  ✅   |              |        |        |         |
| Role Management            |  ✅   |              |        |        |         |
| Permissions                |  ✅   |              |        |        |         |
| Settings                   |  ✅   |              |        |        |         |
| Clinic Settings            |       |      ✅      |        |        |         |
| Profile/My Profile         |       |              |        |   ✅   |   ✅    |

\* For patients, this is labeled as "Patient Home" rather than "Dashboard"

## Detailed Menu Structure by Role

### Admin Role

Administrators have access to all system features and management interfaces:

```
- Dashboard                   (/trq/dashboard)
- All Questionnaires          (/trq/questionnaires)
- Templates                   (/trq/templates)
- Reports                     (/trq/compliance-reports)
- Patients                    (/trq/patients)
- Clients                     (/trq/clients)
- All Users                   (/trq/users)
- Clinics                     (/trq/clinics)
- All Purchases               (/trq/purchases)
- Admin
  - Admin Dashboard           (/trq/admin)
  - Role Management           (/trq/admin/roles)
  - Permissions               (/trq/admin/permissions)
  - Analytics                 (/trq/analytics)
- Settings                    (/trq/settings)
```

### Clinic Admin Role

Clinic Administrators manage day-to-day operations of the clinic:

```
- Dashboard                   (/trq/dashboard)
- Questionnaires              (/trq/questionnaires)
- Templates                   (/trq/templates)
- Compliance Reports          (/trq/compliance-reports)
- Patients                    (/trq/patients)
- Clients                     (/trq/clients)
- Clinic Settings             (/trq/settings)
```

### Doctor Role

Doctors focus on patient care and medical assessments:

```
- Dashboard                   (/trq/dashboard)
- Questionnaires              (/trq/questionnaires)
- Templates                   (/trq/templates)
- Compliance Reports          (/trq/compliance-reports)
- Patients                    (/trq/patients)
```

### Client Role

Clients manage their associated patients and questionnaires:

```
- Dashboard                   (/trq/clients/dashboard)
- Questionnaires              (/trq/questionnaires)
- Patients                    (/trq/patients)
- Profile                     (/trq/profile)
```

### Patient Role

Patients have a simplified interface focused on their own questionnaires:

```
- Patient Home                (/trq/patients/home)
- Questionnaires              (/trq/questionnaires)
- My Profile                  (/trq/profile)
```

## Menu Implementation Notes

### UI Components

The menu structure is implemented using:

- A hierarchical JSON configuration defining available menu items per role
- The SideMenuPage component that renders the appropriate items based on the user's role
- Permission-based rendering to ensure menu items are only displayed if the user has access

### Security Considerations

It's important to note that:

1. **Menu visibility is not security:** While menu items are hidden from users who should not access them, proper security checks must be implemented at the API and route levels to prevent unauthorized access.

2. **Role assumption:** Users with higher-level roles can temporarily assume lower-level roles for testing or support purposes. When a role is assumed, the menu will update to show only the items available to that role.

3. **Dynamic updates:** When a user's role changes (e.g., via admin action), the menu should update automatically upon the next login.

## Testing Menu Navigation

Menu navigation is tested through end-to-end tests that verify:

- Correct menu items are displayed for each role
- Navigation to each menu item works correctly
- Unauthorized access attempts are properly blocked

These tests are located in `e2e/features/navigation/tests/menu-navigation.spec.ts`.
