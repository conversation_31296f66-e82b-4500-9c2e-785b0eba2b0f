# TRQ Medical Questionnaire System: Project Overview

## Introduction and Purpose

The TRQ (Test, Review, Qualify) system is a comprehensive healthcare application designed to streamline the management of medical questionnaires and compliance reporting, primarily for occupational health assessments. The system facilitates the complete lifecycle of questionnaires, from creation and assignment to completion and evaluation, connecting healthcare providers with patients and client organizations.

The core purpose of the TRQ system is to:

1. **Manage and deliver medical questionnaires** to patients based on requirements from client organizations
2. **Generate compliance reports** based on questionnaire responses
3. **Enable doctor review and feedback** on completed questionnaires
4. **Facilitate client purchasing** of questionnaire packages for their employees/patients
5. **Enforce role-based security** to protect sensitive medical information

The system is particularly focused on occupational health scenarios where employers (clients) need to ensure their employees (patients) meet certain health requirements for specific job functions, as evidenced by features related to respirator clearance and work load limitations.

## Core System Components

The TRQ system is built with a modular architecture, where each domain has its own dedicated module with clear responsibilities:

### 1. Questionnaires Module
- Manages questionnaire templates and instances
- Handles questionnaire creation, assignment, and completion
- Provides questionnaire versioning and comparison

### 2. Compliance Reports Module
- Generates compliance reports based on questionnaire responses
- Facilitates doctor review and signoff
- Manages report versioning and statuses

### 3. Patient Module
- Stores patient demographic and health information
- Tracks assigned questionnaires and reports
- Manages patient-doctor relationships

### 4. Client Module
- Handles client company information
- Manages purchases and questionnaire allocations
- Associates patients with client organizations

### 5. Clinic Module
- Manages clinic information
- Associates doctors and clinic administrators
- Handles clinic-specific settings and templates

### 6. Doctor Module
- Manages doctor profiles and specialties
- Facilitates questionnaire review and report generation
- Tracks doctor assignments and workload

### 7. User Management Module
- Handles user authentication and authorization
- Manages user profiles and role assignments
- Supports role assumption for testing purposes

### 8. RBAC (Role-Based Access Control) Module
- Enforces permission checks across the application
- Manages role hierarchies and permissions
- Provides utilities for conditional rendering based on permissions

### 9. Purchases Module
- Tracks questionnaire purchases by clients
- Manages payment information and order history
- Allocates questionnaire licenses to clients

## User Roles and Workflows

The system operates with five distinct user roles organized in a hierarchical structure:

### Admin
**Description:** Technical/IT administrators with full system access
- **Responsibilities:** System configuration, user management, codebase maintenance
- **Key Workflows:**
  - Managing global system settings
  - Creating and managing user accounts
  - Monitoring system health and performance
  - Configuring application-wide parameters

### Clinic Admin
**Description:** Staff managing day-to-day clinic operations
- **Responsibilities:** Doctor management, patient records, client coordination
- **Key Workflows:**
  - Managing doctor schedules and assignments
  - Updating patient and client information
  - Coordinating questionnaire assignments
  - Handling administrative inquiries

### Doctor
**Description:** Healthcare professionals reviewing patient questionnaires
- **Responsibilities:** Patient care, questionnaire review, compliance reporting
- **Key Workflows:**
  - Reviewing completed questionnaires
  - Generating and signing compliance reports
  - Adding medical notes and findings
  - Determining respirator clearance and work limitations

### Client
**Description:** Organizations purchasing questionnaires for their employees
- **Responsibilities:** Questionnaire acquisition, patient management
- **Key Workflows:**
  - Purchasing questionnaire packages
  - Managing associated patients
  - Tracking compliance status
  - Viewing usage reports

### Patient
**Description:** End users completing questionnaires
- **Responsibilities:** Completing assigned questionnaires
- **Key Workflows:**
  - Filling out questionnaires
  - Viewing personal questionnaire history
  - Accessing compliance reports
  - Communicating with healthcare providers

## Technical Architecture

The TRQ system is built using modern web technologies:

### Frontend
- **Framework:** React with TypeScript
- **UI Library:** Material-UI (MUI) with Berry template
- **State Management:** React Context API
- **Routing:** React Router
- **Form Handling:** Formik with Yup validation

### Backend
- **Database:** Firebase Firestore
- **Authentication:** Firebase Authentication
- **Storage:** Firebase Storage (for files/documents)
- **Hosting:** Firebase Hosting
- **Functions:** Firebase Cloud Functions (for server-side logic)

### Project Structure
The codebase follows a modular architecture with a clear separation of concerns:
- `src/[components]/` - Shared reusable UI components
- `src/[constants]/` - Application constants and configuration values
- `src/[contexts]/` - React contexts for state management
- `src/[hooks]/` - Custom React hooks
- `src/[services]/` - Service modules for external integrations
- `src/[types]/` - TypeScript type definitions
- `src/[utils]/` - Utility functions
- Domain-specific modules (e.g., `src/Questionnaires/`, `src/Patient/`) containing:
  - `[pages]/` - Route-level components
  - `[components]/` - Domain-specific components
  - `[services]/` - Domain-specific services
  - `[types]/` - Domain-specific types
  - `[contexts]/` - Domain-specific contexts

## Key Features

### Questionnaire Management
- **Template Creation:** Design reusable questionnaire templates with various question types
- **Template Versioning:** Track changes to templates over time
- **Conditional Questions:** Configure questions that appear based on previous answers
- **Section-Based Organization:** Group questions into logical sections
- **Progress Tracking:** Save and resume questionnaire completion

### Compliance Reporting
- **Automated Report Generation:** Create initial draft reports from questionnaire responses
- **Medical Findings:** Record and categorize medical issues with severity levels
- **Work Limitations:** Specify respirator clearance and work load limitations
- **Digital Signatures:** Allow doctors to digitally sign and approve reports
- **Version Control:** Track changes to reports over time
- **PDF Export:** Generate printable compliance report documents

### Role-Based Access Control
- **Permission Enforcement:** Check permissions at UI, route, and service levels
- **Role Hierarchy:** Implement hierarchical roles (Admin > Clinic Admin > Doctor > Client > Patient)
- **Role Assumption:** Allow higher-level roles to temporarily assume lower-level roles for testing
- **Dynamic UI Rendering:** Show or hide UI elements based on user permissions
- **Route Protection:** Prevent unauthorized access to protected routes

### Patient Management
- **Demographic Information:** Store patient personal and contact details
- **Medical History:** Track height, weight, and other medical information
- **Questionnaire Assignment:** Assign questionnaires to patients
- **Doctor Assignment:** Link patients with specific doctors
- **Client Association:** Associate patients with client organizations

### Purchase Management
- **Package Pricing:** Configure pricing for questionnaire templates
- **Order Processing:** Track purchase orders and payment status
- **License Allocation:** Manage quantity of questionnaires available to clients
- **Transaction History:** Maintain record of all purchase transactions

### Activity Tracking and Notifications
- **User Activity Logging:** Record significant user actions for audit purposes
- **Alert System:** Display notifications for important events
- **Activity Feed:** Show recent activities in a chronological feed
- **Notification Preferences:** Configure notification settings for different event types

## Data Model

The system's data is organized around several key entities:

### Users and Roles
- `users` collection - Core user authentication and profile data
- Role-specific collections (`doctors`, `clinicAdmins`, `clients`) - Denormalized user data for specific roles

### Questionnaires
- `questionnaireTemplates` collection - Reusable questionnaire definitions
  - Template metadata (name, description, price)
  - Questions as a subcollection with various question types
- `questionnaires` collection - Instances of questionnaires assigned to patients
  - Metadata about assignment and completion status
  - Answers stored as a subcollection
  - Comments from healthcare providers as a subcollection

### Medical Data
- `complianceReports` collection - Medical evaluation reports
  - Clearance information (respirator, work load)
  - Medical findings
  - Doctor signature and approval status
- Duplicated in questionnaire subcollections for easier data access

### Business Entities
- `clinics` collection - Medical facility information
- `patients` collection - Patient demographic and health data
- `purchases` collection - Questionnaire purchase records

## Security and Access Control

The application implements a comprehensive security model:

### Authentication
- Email/password authentication through Firebase
- Session management and token-based authentication
- Password security enforcement

### Authorization
- Role-based access control (RBAC) for all resources
- Permission checks at UI, route, and service levels
- Data access rules enforced at the database level
- Hierarchical permission model

### Data Protection
- Field-level access control
- Data validation before persistence
- Audit logging for sensitive operations

## Development and Testing Approach

### Development Workflow
- Feature-based development cycle with planning, implementation, and refinement phases
- Comprehensive documentation for each module
- Standardized code organization and naming conventions

### Testing Strategy
- End-to-end (E2E) testing with Playwright
- Role-specific test scenarios
- Automated test user creation
- Page object model pattern for maintainable tests

## Future Enhancements

Potential areas for future development include:

1. **Enhanced Analytics** - Advanced reporting on questionnaire completion trends and compliance statistics
2. **Integrated Scheduling** - Appointment booking for follow-up consultations
3. **Mobile Application** - Native mobile experience for patients completing questionnaires
4. **AI-Assisted Compliance** - Machine learning to assist in compliance evaluation
5. **Electronic Health Record Integration** - Seamless connection with existing EHR systems

## Conclusion

The TRQ system provides a comprehensive solution for managing medical questionnaires and compliance reporting in occupational health settings. Its modular architecture, role-based security, and feature-rich components create a flexible platform that connects healthcare providers, patients, and client organizations in a secure and efficient workflow.
