# Admin Module

## Overview
The Admin module provides administration capabilities for system administrators. It contains components and functionality for managing users, roles, permissions, and system settings.

## Key Components
- **AdminDashboard**: Main dashboard for administrators showing system statistics and key information
- **UserDetails**: Detailed view of user information and settings
- **PermissionsManagement**: Interface for managing system permissions
- **RoleManagement**: Interface for managing user roles and their associated permissions

## Routes
The Admin module has the following routes:
- `/trq/admin`: Main admin dashboard
- `/trq/admin/dashboard`: Admin dashboard (alias)
- `/trq/admin/trq-dashboard`: TRQ specific dashboard view
- `/trq/admin/user/:id`: User details page for specific user
- `/trq/admin/permissions`: Permissions management page
- `/trq/admin/roles` and `/trq/admin/role-management`: Role management page
- `/trq/admin/rbac-debug`: RBAC debugging tools
- `/trq/admin/fix-imports`: Import fixing utilities

## Integration Points
- Uses the RBAC module for permission checks
- Interacts with Firebase for user data storage
- Connected to the Navigation module for menu organization

## Data Flow
1. Admin authenticates through Authentication module
2. Admin navigates to relevant admin section
3. Admin performs administrative tasks such as user management, role assignment, or system configuration
4. Changes are persisted to the database through Firebase services

## Dependencies
- RBAC module
- Firebase module
- UserManagement module 