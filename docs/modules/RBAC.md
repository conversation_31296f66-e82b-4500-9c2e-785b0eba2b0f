# Role-Based Access Control (RBAC) Module

## Overview
The RBAC module manages role-based access control throughout the application. It provides components, hooks, and utilities for enforcing permissions based on user roles and for role assumption (temporarily taking on another role for testing or support purposes).

## Key Components
- **PermissionRoute**: Route wrapper to control access based on permissions
- **DebugPermissionRoute**: Enhanced permission route with debugging capabilities
- **RoleContext**: Context provider for role management
- **PermissionGuard**: Component to conditionally render content based on permissions
- **WithPermission**: HOC for permission-based component rendering
- **RoleGuard**: Route guard specific to role requirements
- **RoleAssumption**: Components to facilitate role switching for admins

## Integration Points
- Integrates with Authentication module for user identity
- Integrated throughout the application for access control
- Used by Navigation module for menu filtering

## Available Hooks
- **useRole**: Hook to access current role and role assumption functions
- **usePermission**: Hook to check if user has specific permissions
- **useRoleAccess**: Hook to check role-based access
- **useFilterMenuByPermission**: Hook to filter menus based on permissions

## Permission Structure
Permissions are defined by:
- **Resources**: The objects or entities being accessed (e.g., patients, questionnaires)
- **Actions**: The operations performed on those resources (e.g., read, write, delete)

## Role Assumption
Role assumption allows administrators to temporarily assume different roles to:
- Test the application from different user perspectives
- Support users by seeing what they see
- Debug permission issues

## Dependencies
- Authentication module
- UserManagement module 