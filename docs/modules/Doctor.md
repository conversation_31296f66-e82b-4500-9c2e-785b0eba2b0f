# Doctor Module Documentation

## Overview

The Doctor module provides functionality for healthcare providers to view and manage patient data, questionnaires, and medical information in the TRQ system. This module serves as the central access point for doctors to review patient information, manage questionnaires, and handle day-to-day clinical tasks.

## Components

### DoctorDashboard

The main dashboard for doctors, located at `src/Doctor/[pages]/DoctorDashboard.tsx`. This dashboard provides:

- Overview of assigned patients
- Statistics on pending and completed questionnaire reviews
- Upcoming appointments and patient alerts
- Recent activity tracking
- Quick access to common doctor functions

The dashboard contains three main tabs:
1. **Overview** - Displays statistics and recent activity
2. **My Patients** - Shows a filterable list of the doctor's patients
3. **Pending Reviews** - Lists questionnaires that need doctor review

### Routes

Doctor-specific routes are defined in two places:

1. `src/Doctor/routes.tsx` - Module-specific routes
2. `src/Routing/doctorRoutes.tsx` - Role-based dashboard route

## Features

- **Patient Management**: View and search assigned patients
- **Questionnaire Reviews**: See pending and completed questionnaire reviews
- **Activity Tracking**: View recent actions and notifications
- **Quick Access**: Navigate to key areas like patient directory, questionnaire templates, messages, and medical records

## Permissions

The Doctor dashboard requires the VIEW_PATIENT permission to access. This is enforced through the PermissionRoute component.

## Usage

Doctors can access their dashboard by:

1. Logging in with Doctor role credentials
2. Navigating to `/trq/dashboard` (automatic for Doctor role)

## Related Components

- **Patient Module**: Used for patient detail views
- **Questionnaire Module**: For reviewing and assigning questionnaires
- **RBAC System**: For role-based access control

## Future Enhancements

Future versions may include:
- Enhanced appointment scheduling
- Real-time patient monitoring
- Integration with external medical systems
- Clinical decision support tools
- Patient outcome tracking

## Technical Notes

- Uses the Material-UI component library
- Implements responsive design for various screen sizes
- Uses React hooks for state management
- Integrates with Firebase for user and patient data
- Implements role-based access control through RBAC module
