# Authentication Module

## Overview
The Authentication module handles user authentication, registration, password management, and session handling. It uses Firebase Authentication as the backend and provides UI components for login, registration, password reset, and other authentication-related operations.

## Key Components
- **TRQLoginPage**: Login page with email/password and social authentication options
- **TRQRegisterPage**: Registration page for new users
- **TRQForgotPasswordPage**: Password recovery request page
- **TRQResetPasswordPage**: Page to set a new password after reset

## Routes
The Authentication module has the following routes:
- `/`: Default route, redirects to login
- `/trq/login`: Main login page
- `/trq/register`: User registration page
- `/trq/forgot-password`: Password recovery page
- `/trq/reset-password`: Password reset page

## Integration Points
- Uses Firebase Authentication for authentication services
- Integrates with RBAC for role-based access control
- Connected to the User Management module for user profile creation

## Authentication Flow
1. User navigates to login page
2. User authenticates with credentials or social providers
3. On successful authentication, user profile is loaded
4. User is redirected to appropriate dashboard based on role
5. JWT token is stored for session management

## Features
- Email/password authentication
- Social authentication (Google, Facebook, Twitter)
- Password reset via email
- Registration with email verification
- Session persistence
- Role-based redirection

## Dependencies
- Firebase module
- RBAC module
- UserManagement module 