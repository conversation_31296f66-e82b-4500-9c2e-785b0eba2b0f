# Role-Based Access Control (RBAC) System

This document explains how to use the role-based access control system to secure different parts of the application.

## Overview

The RBAC system provides a way to restrict access to resources based on the roles assigned to users. The system consists of:

1. **Roles**: Predefined user roles (<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Doctor, Client, Patient)
2. **Resources**: Items that can be accessed (users, questionnaires, patients, reports, etc.)
3. **Actions**: Operations that can be performed on resources (create, read, update, delete, list, manage)
4. **Permissions**: Combinations of resources and actions that determine what a role can do

## Role Hierarchy and Assumption

The system implements a hierarchical role structure where higher-level roles can assume lower-level roles:

```
Admin -> Clinic Admin -> Doctor -> Client -> Patient
```

### Role Assumption Rules

1. **Admin**
   - Can assume any role in the system
   - Has full access to all resources and actions

2. **Clinic Admin**
   - Can assume Doctor, Client, or Patient roles
   - Cannot assume Admin role
   - Has access to clinic-level resources and actions

3. **Doctor**
   - Can assume Client or Patient roles
   - Cannot assume Admin or Clinic Admin roles
   - Has access to patient care resources and actions

4. **Client**
   - Can assume Patient role
   - Cannot assume Admin, Clinic Admin, or Doctor roles
   - Has limited access to patient-related resources

5. **Patient**
   - Cannot assume any other role
   - Has access only to their own resources

### Using Role Assumption

Role assumption is available through the profile menu in the header. Users with appropriate permissions will see a "Assume Role" button that allows them to switch to a lower-level role.

When assuming a role:
- The user's permissions are temporarily changed to match the assumed role
- The UI adapts to show only the features available to the assumed role
- A notification is shown to confirm the role change
- The original role is preserved and can be restored by logging out and back in

## Key Components

### 1. Permission Constants

All permission-related constants are defined in `src/constants/permissions.ts`:

```typescript
// Resources
RESOURCES.USERS
RESOURCES.QUESTIONNAIRES 
RESOURCES.PATIENTS
// etc.

// Actions
ACTIONS.CREATE
ACTIONS.READ
ACTIONS.UPDATE
// etc.

// Predefined permission combinations
PERMISSIONS.VIEW_USERS
PERMISSIONS.CREATE_QUESTIONNAIRE
// etc.
```

### 2. usePermission Hook

The `usePermission` hook is the primary way to check permissions in functional components:

```typescript
import usePermission from 'hooks/usePermission';

const MyComponent = () => {
  const permission = usePermission();
  
  // Simple checks
  if (permission.canViewUsers()) {
    // Show user list
  }
  
  // Custom resource/action checks
  if (permission.checkPermission(RESOURCES.REPORTS, ACTIONS.CREATE)) {
    // Show create report button
  }
  
  // Dynamic checks (for user-specific resources)
  if (permission.canViewOwnProfile(currentUserId, profileId)) {
    // Show profile
  }
  
  return (
    // Component JSX
  );
};
```

### 3. Conditional Rendering Components

For component-based conditional rendering:

#### PermissionGuard

```tsx
import PermissionGuard from 'components/PermissionGuard';

<PermissionGuard 
  resource={RESOURCES.USERS}
  action={ACTIONS.READ}
  fallback={<AccessDeniedMessage />}
>
  <UserList />
</PermissionGuard>
```

#### ConditionalRender

```tsx
import ConditionalRender from 'components/ConditionalRender';

<ConditionalRender
  resource={RESOURCES.REPORTS}
  action={ACTIONS.CREATE}
>
  <CreateReportButton />
</ConditionalRender>
```

#### NavItemWithPermission

For navigation menus:

```tsx
import NavItemWithPermission from 'components/NavItemWithPermission';
import DashboardIcon from '@mui/icons-material/Dashboard';

<NavItemWithPermission
  resource={RESOURCES.REPORTS}
  action={ACTIONS.LIST}
  to="/reports"
  icon={<DashboardIcon />}
  primary="Reports"
/>
```

### 4. HOC Pattern

For class components or when you want to add permissions to existing components:

```tsx
import { withPermission } from 'components/WithPermission';
import { RESOURCES, ACTIONS } from 'constants/permissions';

const UsersList = () => {
  // Component implementation
};

// Create a permission-protected version
export default withPermission(UsersList, RESOURCES.USERS, ACTIONS.LIST);
```

## Route Protection

For protected routes, you can use PermissionGuard with React Router:

```tsx
<Route
  path="/reports"
  element={
    <PermissionGuard
      resource={RESOURCES.REPORTS}
      action={ACTIONS.LIST}
      fallback={<Navigate to="/unauthorized" />}
    >
      <ReportsPage />
    </PermissionGuard>
  }
/>
```

## Working with Dynamic Resources

Some resources require additional context to check permissions (like checking if a user owns a resource):

```tsx
<PermissionGuard
  resource={RESOURCES.OWN_PROFILE}
  action={ACTIONS.UPDATE}
  contextData={{ 
    userId: currentUser.id, 
    profileId: profile.id 
  }}
>
  <EditProfileButton />
</PermissionGuard>
```

## Best Practices

1. **Use Constants**: Always use the predefined constants for resources and actions
2. **Keep UI Consistent**: Hide or disable elements based on permissions, but keep the overall layout consistent
3. **Double-Check Server-Side**: Always verify permissions on the server as well
4. **Use Specialized Hooks**: For common permission patterns, use the specialized methods in the usePermission hook
5. **Consider Fallbacks**: Provide meaningful fallbacks for users who don't have access to certain features
6. **Role Assumption**: When implementing role assumption, ensure that:
   - The UI clearly indicates the current assumed role
   - Users can easily return to their original role
   - All permission checks respect the assumed role
   - Sensitive operations are still protected by the original role's permissions

## Adding New Permissions

To add new permissions:

1. Add any new resources or predefined permissions to `src/constants/permissions.ts`
2. Update the role permissions in `src/models/Permission.ts`
3. If needed, add new convenience methods to the `usePermission` hook 