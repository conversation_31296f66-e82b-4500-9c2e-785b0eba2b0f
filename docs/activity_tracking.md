# Activity Tracking and Notifications System

This document outlines the activity tracking and notifications system implemented in the application. This system provides a robust way to track user activities and display personalized notifications based on those activities.

## Table of Contents

1. [Overview](#overview)
2. [Activity Tracking](#activity-tracking)
3. [Notification System](#notification-system)
4. [Integration Guidelines](#integration-guidelines)
5. [Components](#components)
6. [Router Integration](#router-integration)
7. [Usage Examples](#usage-examples)

## Overview

The activity tracking and notifications system consists of two main components:

1. **Activity Tracking**: Records user interactions, system events, and state changes in a structured way
2. **Notifications**: Displays alerts to users based on activity events or system events

This system provides several benefits:
- Audit trail of user activities for compliance and troubleshooting
- Real-time notifications to keep users informed of important events
- Activity feed to review recent activities and important changes
- Personalized user experience with relevant notifications
- Analytics data for understanding user behavior and application usage

## Activity Tracking

The activity tracking system captures and records various types of activities in the application.

### Activity Types

Activities are categorized by type:

- `PAGE_VIEW`: User views a page or screen
- `BUTTON_CLICK`: User clicks a button or interactive element
- `FORM_SUBMIT`: User submits a form
- `DATA_CHANGE`: Data is created, updated, or deleted
- `AUTHENTICATION`: User logs in, logs out, or performs auth-related actions
- `NOTIFICATION`: Notifications are created, read, or dismissed
- `RESOURCE_ACCESS`: User accesses a particular resource
- `SYSTEM_EVENT`: System-level events like initialization or errors
- `CUSTOM`: Custom activities that don't fit other categories

### Activity Status

Each activity can have a status:

- `STARTED`: Activity has begun but isn't complete
- `COMPLETED`: Activity finished successfully
- `FAILED`: Activity attempted but failed
- `CANCELLED`: Activity was cancelled before completion

### Activity Data Structure

Activities include the following information:

```typescript
interface Activity {
  id?: string;
  timestamp: Date;
  userId?: string;
  userName?: string;
  type: ActivityType;
  description: string;
  status: ActivityStatus;
  resourceId?: string;
  resourceType?: string;
  location?: string; // URL or route path
  metadata?: Record<string, any>;
  duration?: number; // in milliseconds
  relatedActivityId?: string; // for tracking related activities
}
```

## Notification System

The notification system displays alerts to users based on activities or system events.

### Notification Types

Notifications are categorized by severity:

- `INFO`: Informational notifications
- `SUCCESS`: Success notifications
- `WARNING`: Warning notifications
- `ERROR`: Error notifications

### Notification Sources

Notifications can come from different sources:

- `SYSTEM`: System-generated notifications
- `USER`: User-to-user notifications
- `DATABASE`: Notifications triggered by database events

### Notification Options

Notifications can be configured with options:

- `autoHideDuration`: How long to display the notification before auto-hiding
- `preventDuplicates`: Prevent duplicate notifications
- `requireAcknowledgement`: Require user acknowledgement to dismiss

## Integration Guidelines

When integrating the activity tracking and notifications system, follow these guidelines:

### When to Track Activities

Track activities in these scenarios:

1. **Navigation**: Track page views when users navigate to new pages
2. **User Interactions**: Track significant user interactions (form submissions, important button clicks)
3. **Data Changes**: Track when data is created, updated, or deleted
4. **Authentication**: Track login, logout, and authentication events
5. **System Events**: Track important system events or state changes

### When to Create Notifications

Create notifications in these scenarios:

1. **Important Updates**: Notify users of important system or data updates
2. **Action Required**: Notify users when their action is required
3. **Action Completed**: Notify users when an async action completes
4. **Errors**: Notify users of errors that require attention
5. **System Status**: Notify users of system status changes

### Tracking with Context

Always provide sufficient context in your activity tracking:

1. **Descriptive Messages**: Use clear, descriptive messages
2. **Resource Information**: Include resource type and ID where applicable
3. **Metadata**: Include relevant metadata for additional context
4. **User Information**: Include user information when available
5. **Status**: Set the appropriate status for the activity

## Components

The activity tracking and notification system includes several key components:

### Models

- `Activity.ts`: Defines activity data structures
- `Alert.ts`: Defines notification data structures

### Services

- `activityService.ts`: Provides methods for tracking and querying activities
- `alertService.ts`: Provides methods for creating and managing notifications

### Contexts

- `ActivityContext.tsx`: Provides activity tracking functionality throughout the app
- `AlertContext.tsx`: Provides notification functionality throughout the app

### UI Components

- `ActivityFeed.tsx`: Displays a feed of activities
- `AlertDisplay.tsx`: Displays notifications to users
- `PageViewTracker.tsx`: A component that automatically tracks page views when routes change

## Router Integration

The activity tracking system is designed to work both inside and outside of React Router contexts:

- `ActivityProvider`: This context provider is router-independent and can be placed at the app's root level
- `PageViewTracker`: This component must be used within a Router context to track page views automatically
- `useActivityPageTracking`: A hook to manually track page views in components that have access to routing

### Example of Router Integration

```tsx
// In a layout component
import PageViewTracker from 'ui-component/activity/PageViewTracker';

const MainLayout = () => {
  return (
    <div>
      {/* This component doesn't render anything visible but tracks page views */}
      <PageViewTracker />
      
      {/* Rest of your layout */}
    </div>
  );
};
```

## Usage Examples

### Tracking Page Views

```tsx
// In a route component
import { useActivity } from 'contexts/ActivityContext';
import { useEffect } from 'react';

const DashboardPage = () => {
  const { trackPageView } = useActivity();
  
  useEffect(() => {
    trackPageView('Dashboard');
  }, [trackPageView]);
  
  return <div>Dashboard Content</div>;
};
```

### Tracking Button Clicks

```tsx
// In a component with a button
import { useActivity } from 'contexts/ActivityContext';
import { Button } from '@mui/material';

const SubmitButton = () => {
  const { trackButtonClick } = useActivity();
  
  const handleClick = () => {
    trackButtonClick('submit', 'User clicked submit button');
    // Rest of click handler logic
  };
  
  return <Button onClick={handleClick}>Submit</Button>;
};
```

### Tracking Form Submissions

```tsx
// In a form component
import { useActivity } from 'contexts/ActivityContext';
import { ActivityStatus } from 'types/Activity';

const UserForm = () => {
  const { trackFormSubmit } = useActivity();
  
  const handleSubmit = async (values) => {
    try {
      // Start tracking the activity
      await trackFormSubmit('user-profile', ActivityStatus.STARTED);
      
      // Submit form data
      await userService.updateProfile(values);
      
      // Complete the activity
      await trackFormSubmit('user-profile', ActivityStatus.COMPLETED, {
        formValues: values
      });
      
      // Show success notification
    } catch (error) {
      // Track failure
      await trackFormSubmit('user-profile', ActivityStatus.FAILED, {
        error: error.message
      });
      
      // Show error notification
    }
  };
  
  return <form onSubmit={handleSubmit}>...</form>;
};
```

### Tracking Data Changes

```tsx
// When updating data
import { useActivity } from 'contexts/ActivityContext';

const EditProduct = () => {
  const { trackDataChange } = useActivity();
  
  const handleUpdate = async (productId, data) => {
    await productService.updateProduct(productId, data);
    
    await trackDataChange(
      'product',
      productId,
      `Updated product ${data.name}`,
      { changedFields: Object.keys(data) }
    );
  };
};
```

### Displaying an Activity Feed

```tsx
// In a dashboard or user profile component
import ActivityFeed from 'ui-component/ActivityFeed';

const UserProfile = () => {
  return (
    <div>
      <h2>User Profile</h2>
      {/* Other profile content */}
      
      <ActivityFeed 
        title="Recent Activity"
        maxItems={5}
        filter={{ userId: currentUserId }}
        showFilter={true}
      />
    </div>
  );
};
```

### Creating Custom Notifications

```tsx
// Creating a custom notification from an activity
import { useAlerts } from 'contexts/AlertContext';

const NotificationDemo = () => {
  const { createAlert } = useAlerts();
  
  const handleAction = async () => {
    // Perform some action
    
    // Create a notification
    await createAlert(
      'Action Completed',
      'Your requested action has been completed successfully',
      'success',
      'SYSTEM',
      { actionType: 'demo' },
      { autoHideDuration: 5000 }
    );
  };
};
``` 