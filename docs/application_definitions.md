# Role Definitions & Relationships

This document defines the roles in a doctor’s office application and how each role relates to one another. It also notes any assumptions made in interpreting these roles.

## Table of Contents
1. [Overview](#overview)
2. [Roles](#roles)
   - [1. Admin](#1-admin)
   - [2. Clinic Admin](#2-clinic-admin)
   - [3. Doctor](#3-doctor)
   - [4. Client](#4-client)
   - [5. Pat<PERSON>](#5-patient)
3. [Role Relationships](#role-relationships)
4. [Assumptions](#assumptions)

---

## Overview

In this system, users have different roles that dictate what they can see and do.  
- **Admin**: Oversees and manages the entire application (technical/IT perspective).  
- **Clinic Admin**: Manages day-to-day administrative tasks within the clinic.  
- **Doctor**: Provides medical services, reviews patient questionnaires, and generates feedback.  
- **Client**: A company or organization that purchases questionnaires for its **Patients**.  
- **Patient**: The end user who fills out questionnaires and receives compliance reports.

---

## Roles

### 1. Admin

- **Description**:  
  - The IT personnel or system administrator who has full access and control of the software application.
- **Responsibilities**:  
  - Manage the platform at a technical level (e.g., configure system settings, update the application).  
  - Grant or revoke access and permissions for other roles.  
  - Perform any high-level maintenance or code deployments (if needed).
- **Key Capabilities**:  
  - **All** system capabilities—there are no restrictions.
- **Limitations**:  
  - While the Admin can see and do everything, their primary focus is not daily clinic operations but rather overall application support and maintenance.

---

### 2. Clinic Admin

- **Description**:  
  - An employee at the doctor’s office responsible for administrative and operational tasks.
- **Responsibilities**:  
  - View and manage doctors’ details (e.g., schedules, assignments) within the clinic.  
  - Edit or update patient and client information.  
  - Ensure the application runs smoothly from the clinic’s perspective (e.g., resolving scheduling conflicts, handling patient or client inquiries).
- **Key Capabilities**:  
  - Manage patient records, including demographics and basic medical info if needed.  
  - Manage client information (e.g., track which clients are associated with which patients).  
  - Access to relevant reports or questionnaires as needed to facilitate operations.
- **Limitations**:  
  - Cannot make application-level code changes (unlike Admin).  
  - Typically restricted to clinic-level oversight rather than system-wide settings.

---

### 3. Doctor

- **Description**:  
  - A healthcare professional employed or partnered with the doctor’s office.
- **Responsibilities**:  
  - Manage and treat patients assigned to them.  
  - Review questionnaires and compliance reports filled out by patients.  
  - Provide feedback, medical notes, or instructions based on the compliance report results.
- **Key Capabilities**:  
  - Full access to patient medical data and questionnaire results relevant to their practice.  
  - Can approve or disapprove certain treatment plans or compliance steps based on the report.
- **Limitations**:  
  - Does not typically handle billing or high-level clinic administration (unless designated by the clinic).  
  - Does not manage the overall application settings.

---

### 4. Client

- **Description**:  
  - A company or organization that purchases questionnaires for its associated patients.
- **Responsibilities**:  
  - Acquire and provide questionnaires for their patient group.  
  - Manage (or coordinate with the clinic) which patients are eligible for which questionnaires.
- **Key Capabilities**:  
  - View relevant data for their patients (depending on privacy settings and agreements).  
  - Access usage and purchase reports for questionnaires.
- **Limitations**:  
  - Limited access to clinical or medical details—only what’s necessary to track questionnaire usage and compliance.  
  - Does not directly treat or manage patient health.

---

### 5. Patient

- **Description**:  
  - An individual who takes the questionnaires and receives medical services or feedback from the doctor.
- **Responsibilities**:  
  - Complete questionnaires related to their healthcare.  
  - Follow up on compliance reports or feedback from the doctor.
- **Key Capabilities**:  
  - Access their own questionnaire results and compliance reports.  
  - Communicate with the clinic or doctor as needed (e.g., scheduling, clarifications).
- **Limitations**:  
  - No administrative privileges.  
  - Limited to personal health and questionnaire data.

---

## Role Relationships

1. **Admin → Everyone**  
   - The Admin manages overall permissions and system settings that affect all other roles.

2. **Clinic Admin → Doctor, Patient, Client**  
   - The Clinic Admin oversees day-to-day operations, managing details for doctors, patients, and clients.  
   - Works closely with Doctors to ensure patient records are accurate.  
   - Coordinates with Clients regarding patient details or questionnaire usage.

3. **Doctor → Patient**  
   - The Doctor provides care, reviews questionnaires, and generates compliance reports.  
   - Patients rely on Doctors for medical guidance based on questionnaire findings.

4. **Client → Patient**  
   - The Client pays for questionnaires used by Patients.  
   - Has a financial or service-providing relationship but is not involved directly in medical decisions.

5. **Patient → Doctor**  
   - Patients take questionnaires and await feedback or compliance instructions from the Doctor.

---

## Assumptions

- **Data Privacy**: Each role only has access to information necessary for their responsibilities (e.g., Patients can only see their own data, Clients see aggregated or relevant patient data).
- **Billing & Payments**: It is assumed that Clients handle the financial aspects of purchasing questionnaires; Patients do not pay individually (unless specified otherwise by the clinic).
- **Technical Control**: Admin has full system privileges (installation, updates, user management). No other roles can make code-level changes.
- **Multiple Clinics**: If the application supports multiple clinics, each Clinic Admin would only manage their respective clinic, not others.
- **Doctor-Patient Assignments**: A patient can be assigned to a specific doctor (or multiple doctors in some practices) as needed. This is managed by the Clinic Admin role.

---

*End of Document*  
