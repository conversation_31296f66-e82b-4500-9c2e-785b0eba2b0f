# Patient Questionnaire Flow

This document outlines the flow for patients taking questionnaires in our system, including the requirement that a company/client must purchase questionnaires before patients can use them.

## Flow Diagram

```mermaid
flowchart TD
    %% Main entities
    Client[Client/Company]
    Purchase[Purchase Process]
    System[System]
    Patient[Patient]
    Doctor[Healthcare Provider]
    
    %% Client purchase flow
    Client -->|Selects questionnaires| Purchase
    Purchase -->|Provides payment| System
    System -->|Records purchase transaction| DB[(Database)]
    System -->|Makes questionnaires available| Client
    
    %% Assignment flow
    Client -->|Assigns questionnaire to patient| Patient
    Doctor -->|Can also assign questionnaire| Patient
    
    %% Patient flow
    Patient -->|Receives notification| Notification{Notification}
    Notification -->|Opens link| AccessQuestionnaire[Access Questionnaire]
    AccessQuestionnaire -->|Authentication| Auth{Authenticated?}
    Auth -->|Yes| QuestionnaireWizard[Questionnaire Wizard]
    Auth -->|No| Login[Login Screen]
    Login -->|Successful login| QuestionnaireWizard
    
    %% Questionnaire completion flow
    QuestionnaireWizard -->|Completes questionnaire| SubmitResponses[Submit Responses]
    SubmitResponses -->|Saves responses| DB
    SubmitResponses -->|Updates status| CompletedQuestionnaire[Completed Questionnaire]
    
    %% Review flow
    CompletedQuestionnaire -->|Available for review| Review[Healthcare Provider Review]
    Review -->|May generate report| Report[Compliance Report]
    
    %% Styling
    classDef clientProcess fill:#f9d77e,stroke:#333,stroke-width:1px;
    classDef patientProcess fill:#a8d5ba,stroke:#333,stroke-width:1px;
    classDef systemProcess fill:#d5a8a8,stroke:#333,stroke-width:1px;
    classDef decision fill:#f8f9fa,stroke:#333,stroke-width:1px;
    
    class Client,Purchase clientProcess;
    class Patient,AccessQuestionnaire,QuestionnaireWizard,SubmitResponses,CompletedQuestionnaire patientProcess;
    class System,DB,Review,Report systemProcess;
    class Notification,Auth decision;
```

## Process Description

1. **Purchase Phase**
   - Client/Company selects questionnaires to purchase
   - Client completes payment process
   - System records the purchase transaction
   - Purchased questionnaires become available in the client's account

2. **Assignment Phase**
   - Client/Company assigns questionnaires to specific patients
   - Healthcare providers can also assign questionnaires to patients (if authorized)

3. **Access Phase**
   - Patient receives notification about assigned questionnaire
   - Patient opens the questionnaire link
   - System authenticates the patient
   - Patient accesses the questionnaire wizard

4. **Completion Phase**
   - Patient completes the questionnaire through the wizard interface
   - System saves patient responses
   - System updates questionnaire status to completed

5. **Review Phase**
   - Healthcare provider reviews completed questionnaire
   - System may generate compliance reports based on responses
   - Reports are made available to authorized personnel

## Key Points

- Questionnaires must be purchased by a client/company before patients can access them
- Assignment can be done by either the client or healthcare providers
- Patient authentication is required to access questionnaires
- Completed questionnaires can be reviewed by healthcare providers
- The system supports generation of compliance reports based on questionnaire data
