## Required Checklist Before Any and Every Change:

**Models**
  [ ] We are extending existing models.
  [ ] We are changing models.
  [ ] We are adding new models.
  [ ] We are deleting or removing models.

**Modules**
  [ ] We are changing module(s)
  [ ] List all modules to be changed:


  [ ] We are adding module(s)
  [ ] List all modules to be added:


  [ ] We are deleting module(s)
  [ ] List all modules to be deleted:

**Components**
[ ] We are changing component(s)
[ ] List all components to be changed:


[ ] We are adding component(s)
[ ] List all components to be added:


[ ] We are deleting component(s)
[ ] List all components to be deleted:





## Required Checklist After Every Change:

- [ ] we started with model changes
- [ ] we stubbed changes where we could
- [ ] We added error handling where necessary.
- [ ] We built with modularity in mind.
- [ ] We didn't place anything where it shouldn't be (e.g., models inside component files).
